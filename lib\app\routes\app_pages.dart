import 'package:get/get.dart';
import '../bindings/bindings.dart';
import '../views/home/<USER>';
import '../views/contacts/contacts_view.dart';
import '../views/voice_setup/voice_setup_view.dart';
import '../views/settings/settings_view.dart';
import '../views/onboarding/onboarding_view.dart';
import '../views/user_setup/user_setup_view.dart';
import 'app_routes.dart';

class AppPages {
  AppPages._();

  static const INITIAL = Routes.ONBOARDING;

  static final routes = [
    GetPage(
      name: _Paths.ONBOARDING,
      page: () => const OnboardingView(),
      binding: OnboardingBinding(),
    ),
    GetPage(
      name: _Paths.USER_SETUP,
      page: () => const UserSetupView(),
      binding: UserSetupBinding(),
    ),
    GetPage(
      name: _Paths.HOME,
      page: () => const HomeView(),
      binding: HomeBinding(),
    ),
    GetPage(
      name: _Paths.CONTACTS,
      page: () => const ContactsView(),
      binding: ContactsBinding(),
    ),
    GetPage(
      name: _Paths.VOICE_SETUP,
      page: () => const VoiceSetupView(),
      binding: VoiceSetupBinding(),
    ),
    GetPage(
      name: _Paths.SETTINGS,
      page: () => const SettingsView(),
      binding: SettingsBinding(),
    ),
  ];
}

abstract class _Paths {
  _Paths._();
  
  static const ONBOARDING = '/onboarding';
  static const USER_SETUP = '/user-setup';
  static const HOME = '/home';
  static const CONTACTS = '/contacts';
  static const VOICE_SETUP = '/voice-setup';
  static const SETTINGS = '/settings';
}
