# Flutter wrapper
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }
-dontwarn io.flutter.embedding.**

# GetX
-keep class com.example.smart_talk.** { *; }
-keep class * extends get.** { *; }
-keepclassmembers class * extends get.** { *; }

# SQLite
-keep class * extends android.database.sqlite.SQLiteOpenHelper { *; }
-keep class * extends android.database.Cursor { *; }

# Speech to Text
-keep class com.google.android.gms.** { *; }
-dontwarn com.google.android.gms.**

# Audio Players
-keep class com.ryanheise.audioservice.** { *; }
-keep class com.ryanheise.audio_session.** { *; }

# Permissions
-keep class com.baseflow.permissionhandler.** { *; }

# Contacts
-keep class cachet.plugins.contacts.** { *; }

# URL Launcher
-keep class io.flutter.plugins.urllauncher.** { *; }

# HTTP
-keep class dart.io.** { *; }

# Shared Preferences
-keep class io.flutter.plugins.sharedpreferences.** { *; }

# Flutter SVG
-keep class com.pichillilorenzo.flutter_inappwebview.** { *; }

# Lottie
-keep class com.airbnb.lottie.** { *; }

# Crypto
-keep class dart.typed_data.** { *; }

# General rules
-keepattributes *Annotation*
-keepattributes SourceFile,LineNumberTable
-keep public class * extends java.lang.Exception
-printmapping mapping.txt
-optimizationpasses 5
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-dontoptimize
-dontpreverify
-verbose