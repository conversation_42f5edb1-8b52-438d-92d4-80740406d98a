import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/controllers.dart';
import '../../models/models.dart';
import '../widgets/widgets.dart';

class ContactsView extends GetView<ContactsController> {
  const ContactsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('جهات الاتصال'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.sync),
            onPressed: controller.syncContactsFromDevice,
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddContactDialog(),
          ),
        ],
      ),
      body: Obx(() => LoadingOverlay(
        isLoading: controller.isLoading.value || controller.isSyncing.value,
        loadingMessage: controller.isSyncing.value 
            ? 'جاري مزامنة جهات الاتصال...' 
            : 'جاري التحميل...',
        child: Column(
          children: [
            // شريط البحث
            _buildSearchBar(),
            
            // الإحصائيات
            _buildStatistics(),
            
            // قائمة جهات الاتصال
            Expanded(
              child: _buildContactsList(),
            ),
          ],
        ),
      )),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddContactDialog(),
        backgroundColor: Get.theme.colorScheme.primary,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'البحث في جهات الاتصال...',
          prefixIcon: const Icon(Icons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          filled: true,
          fillColor: Get.theme.colorScheme.surface,
        ),
        onChanged: (value) {
          controller.searchContacts(value);
        },
      ),
    );
  }

  Widget _buildStatistics() {
    return Obx(() => Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'المجموع',
              controller.totalContacts.value.toString(),
              Icons.contacts,
              Get.theme.colorScheme.primary,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'المفضلة',
              controller.totalFavorites.value.toString(),
              Icons.favorite,
              Colors.red,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'النتائج',
              controller.searchResults.length.toString(),
              Icons.search,
              Colors.orange,
            ),
          ),
        ],
      ),
    ));
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(height: 4),
            Text(
              value,
              style: Get.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Get.textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactsList() {
    return Obx(() {
      final contacts = controller.searchQuery.value.isNotEmpty
          ? controller.searchResults
          : controller.localContacts;

      if (contacts.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.contacts_outlined,
                size: 64,
                color: Get.theme.colorScheme.onSurface.withOpacity(0.5),
              ),
              const SizedBox(height: 16),
              Text(
                controller.searchQuery.value.isNotEmpty
                    ? 'لا توجد نتائج للبحث'
                    : 'لا توجد جهات اتصال',
                style: Get.textTheme.titleMedium?.copyWith(
                  color: Get.theme.colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                controller.searchQuery.value.isNotEmpty
                    ? 'جرب كلمات بحث أخرى'
                    : 'اضغط على + لإضافة جهة اتصال جديدة',
                style: Get.textTheme.bodyMedium?.copyWith(
                  color: Get.theme.colorScheme.onSurface.withOpacity(0.5),
                ),
              ),
            ],
          ),
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(16.0),
        itemCount: contacts.length,
        itemBuilder: (context, index) {
          final contact = contacts[index];
          return _buildContactCard(contact);
        },
      );
    });
  }

  Widget _buildContactCard(ContactModel contact) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 8.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Get.theme.colorScheme.primary,
          child: Text(
            contact.name.isNotEmpty ? contact.name[0].toUpperCase() : '?',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          contact.name,
          style: Get.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(contact.phoneNumber),
            if (contact.email != null && contact.email!.isNotEmpty)
              Text(
                contact.email!,
                style: Get.textTheme.bodySmall?.copyWith(
                  color: Get.theme.colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: Icon(
                contact.isFavorite ? Icons.favorite : Icons.favorite_border,
                color: contact.isFavorite ? Colors.red : null,
              ),
              onPressed: () => controller.toggleFavorite(contact.id!),
            ),
            PopupMenuButton<String>(
              onSelected: (value) => _handleContactAction(value, contact),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'call',
                  child: ListTile(
                    leading: Icon(Icons.phone),
                    title: Text('اتصال'),
                  ),
                ),
                const PopupMenuItem(
                  value: 'sms',
                  child: ListTile(
                    leading: Icon(Icons.message),
                    title: Text('رسالة نصية'),
                  ),
                ),
                const PopupMenuItem(
                  value: 'whatsapp',
                  child: ListTile(
                    leading: Icon(Icons.chat),
                    title: Text('واتساب'),
                  ),
                ),
                const PopupMenuItem(
                  value: 'edit',
                  child: ListTile(
                    leading: Icon(Icons.edit),
                    title: Text('تعديل'),
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: ListTile(
                    leading: Icon(Icons.delete, color: Colors.red),
                    title: Text('حذف', style: TextStyle(color: Colors.red)),
                  ),
                ),
              ],
            ),
          ],
        ),
        onTap: () => _showContactDetails(contact),
      ),
    );
  }

  void _handleContactAction(String action, ContactModel contact) {
    switch (action) {
      case 'call':
        controller.makeCall(contact.phoneNumber);
        break;
      case 'sms':
        _showSendMessageDialog(contact, false);
        break;
      case 'whatsapp':
        _showSendMessageDialog(contact, true);
        break;
      case 'edit':
        _showEditContactDialog(contact);
        break;
      case 'delete':
        _showDeleteContactDialog(contact);
        break;
    }
  }

  void _showContactDetails(ContactModel contact) {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          color: Get.theme.colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircleAvatar(
              radius: 40,
              backgroundColor: Get.theme.colorScheme.primary,
              child: Text(
                contact.name.isNotEmpty ? contact.name[0].toUpperCase() : '?',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              contact.name,
              style: Get.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              contact.phoneNumber,
              style: Get.textTheme.titleMedium,
            ),
            if (contact.email != null && contact.email!.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                contact.email!,
                style: Get.textTheme.bodyMedium?.copyWith(
                  color: Get.theme.colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            ],
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildActionButton(
                  Icons.phone,
                  'اتصال',
                  () {
                    Get.back();
                    controller.makeCall(contact.phoneNumber);
                  },
                ),
                _buildActionButton(
                  Icons.message,
                  'رسالة',
                  () {
                    Get.back();
                    _showSendMessageDialog(contact, false);
                  },
                ),
                _buildActionButton(
                  Icons.chat,
                  'واتساب',
                  () {
                    Get.back();
                    _showSendMessageDialog(contact, true);
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(IconData icon, String label, VoidCallback onPressed) {
    return Column(
      children: [
        IconButton(
          onPressed: onPressed,
          icon: Icon(icon),
          iconSize: 32,
          style: IconButton.styleFrom(
            backgroundColor: Get.theme.colorScheme.primaryContainer,
            foregroundColor: Get.theme.colorScheme.primary,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: Get.textTheme.bodySmall,
        ),
      ],
    );
  }

  void _showAddContactDialog() {
    final nameController = TextEditingController();
    final phoneController = TextEditingController();
    final emailController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('إضافة جهة اتصال'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'الاسم *',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: phoneController,
              decoration: const InputDecoration(
                labelText: 'رقم الهاتف *',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.phone,
            ),
            const SizedBox(height: 12),
            TextField(
              controller: emailController,
              decoration: const InputDecoration(
                labelText: 'البريد الإلكتروني',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              if (nameController.text.isNotEmpty && phoneController.text.isNotEmpty) {
                controller.addContact(
                  nameController.text,
                  phoneController.text,
                  email: emailController.text.isNotEmpty ? emailController.text : null,
                );
                Get.back();
              }
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  void _showEditContactDialog(ContactModel contact) {
    final nameController = TextEditingController(text: contact.name);
    final phoneController = TextEditingController(text: contact.phoneNumber);
    final emailController = TextEditingController(text: contact.email ?? '');

    Get.dialog(
      AlertDialog(
        title: const Text('تعديل جهة الاتصال'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'الاسم *',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: phoneController,
              decoration: const InputDecoration(
                labelText: 'رقم الهاتف *',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.phone,
            ),
            const SizedBox(height: 12),
            TextField(
              controller: emailController,
              decoration: const InputDecoration(
                labelText: 'البريد الإلكتروني',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              if (nameController.text.isNotEmpty && phoneController.text.isNotEmpty) {
                final updatedContact = contact.copyWith(
                  name: nameController.text,
                  phoneNumber: phoneController.text,
                  email: emailController.text.isNotEmpty ? emailController.text : null,
                  updatedAt: DateTime.now(),
                );
                controller.updateContact(updatedContact);
                Get.back();
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _showSendMessageDialog(ContactModel contact, bool isWhatsApp) {
    final messageController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: Text(isWhatsApp ? 'إرسال رسالة واتساب' : 'إرسال رسالة نصية'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('إلى: ${contact.name}'),
            const SizedBox(height: 12),
            TextField(
              controller: messageController,
              decoration: const InputDecoration(
                labelText: 'الرسالة',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              if (messageController.text.isNotEmpty) {
                if (isWhatsApp) {
                  controller.sendWhatsApp(contact.phoneNumber, messageController.text);
                } else {
                  controller.sendSMS(contact.phoneNumber, messageController.text);
                }
                Get.back();
              }
            },
            child: const Text('إرسال'),
          ),
        ],
      ),
    );
  }

  void _showDeleteContactDialog(ContactModel contact) {
    Get.dialog(
      AlertDialog(
        title: const Text('حذف جهة الاتصال'),
        content: Text('هل أنت متأكد من رغبتك في حذف "${contact.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              controller.deleteContact(contact.id!);
              Get.back();
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
