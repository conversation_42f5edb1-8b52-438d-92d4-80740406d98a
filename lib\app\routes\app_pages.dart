import 'package:get/get.dart';
import '../bindings/bindings.dart';
import '../views/home/<USER>';
import '../views/contacts/contacts_view.dart';
import '../views/voice_setup/voice_setup_view.dart';
import '../views/settings/settings_view.dart';
import '../views/onboarding/onboarding_view.dart';
import '../views/user_setup/user_setup_view.dart';
import 'app_routes.dart';

class AppPages {
  AppPages._();

  static const initial = Routes.onboarding;

  static final routes = [
    GetPage(
      name: _Paths.onboarding,
      page: () => const OnboardingView(),
      binding: OnboardingBinding(),
    ),
    GetPage(
      name: _Paths.userSetup,
      page: () => const UserSetupView(),
      binding: UserSetupBinding(),
    ),
    GetPage(
      name: _Paths.home,
      page: () => const HomeView(),
      binding: HomeBinding(),
    ),
    GetPage(
      name: _Paths.contacts,
      page: () => const ContactsView(),
      binding: ContactsBinding(),
    ),
    GetPage(
      name: _Paths.voiceSetup,
      page: () => const VoiceSetupView(),
      binding: VoiceSetupBinding(),
    ),
    GetPage(
      name: _Paths.settings,
      page: () => const SettingsView(),
      binding: SettingsBinding(),
    ),
  ];
}

abstract class _Paths {
  _Paths._();
  
  static const onboarding = '/onboarding';
  static const userSetup = '/user-setup';
  static const home = '/home';
  static const contacts = '/contacts';
  static const voiceSetup = '/voice-setup';
  static const settings = '/settings';
}
