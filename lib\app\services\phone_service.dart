import 'dart:io';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:contacts_service/contacts_service.dart';
import '../models/models.dart';
import '../services/database_service.dart';

class PhoneService {
  static final PhoneService _instance = PhoneService._internal();
  factory PhoneService() => _instance;
  PhoneService._internal();

  final DatabaseService _databaseService = DatabaseService();
  static const MethodChannel _channel = MethodChannel('smart_talk/phone');

  // إجراء مكالمة هاتفية
  Future<bool> makeCall(String phoneNumber) async {
    try {
      // التحقق من الصلاحيات
      final permission = await Permission.phone.request();
      if (permission != PermissionStatus.granted) {
        throw Exception('Phone permission not granted');
      }

      final uri = Uri(scheme: 'tel', path: phoneNumber);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
        return true;
      } else {
        throw Exception('Could not launch phone dialer');
      }
    } catch (e) {
      print('Error making call: $e');
      return false;
    }
  }

  // إرسال رسالة نصية
  Future<bool> sendSMS(String phoneNumber, String message) async {
    try {
      final uri = Uri(
        scheme: 'sms',
        path: phoneNumber,
        queryParameters: {'body': message},
      );

      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
        return true;
      } else {
        throw Exception('Could not launch SMS app');
      }
    } catch (e) {
      print('Error sending SMS: $e');
      return false;
    }
  }

  // إرسال رسالة واتساب
  Future<bool> sendWhatsApp(String phoneNumber, String message) async {
    try {
      // تنسيق رقم الهاتف للواتساب
      String formattedNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
      if (!formattedNumber.startsWith('+')) {
        formattedNumber = '+$formattedNumber';
      }

      final uri = Uri.parse(
        'https://wa.me/$formattedNumber?text=${Uri.encodeComponent(message)}'
      );

      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        return true;
      } else {
        throw Exception('Could not launch WhatsApp');
      }
    } catch (e) {
      print('Error sending WhatsApp message: $e');
      return false;
    }
  }

  // الحصول على جهات الاتصال من الهاتف
  Future<List<Contact>> getDeviceContacts() async {
    try {
      // التحقق من الصلاحيات
      final permission = await Permission.contacts.request();
      if (permission != PermissionStatus.granted) {
        throw Exception('Contacts permission not granted');
      }

      final contacts = await ContactsService.getContacts();
      return contacts.toList();
    } catch (e) {
      print('Error getting device contacts: $e');
      return [];
    }
  }

  // البحث عن جهة اتصال في الهاتف
  Future<List<Contact>> searchDeviceContacts(String query) async {
    try {
      final permission = await Permission.contacts.request();
      if (permission != PermissionStatus.granted) {
        throw Exception('Contacts permission not granted');
      }

      final contacts = await ContactsService.getContacts(query: query);
      return contacts.toList();
    } catch (e) {
      print('Error searching device contacts: $e');
      return [];
    }
  }

  // إضافة جهة اتصال إلى الهاتف
  Future<bool> addContactToDevice(String name, String phoneNumber, {String? email}) async {
    try {
      final permission = await Permission.contacts.request();
      if (permission != PermissionStatus.granted) {
        throw Exception('Contacts permission not granted');
      }

      final contact = Contact(
        givenName: name,
        phones: [Item(label: 'mobile', value: phoneNumber)],
        emails: email != null ? [Item(label: 'work', value: email)] : [],
      );

      await ContactsService.addContact(contact);
      return true;
    } catch (e) {
      print('Error adding contact to device: $e');
      return false;
    }
  }

  // إضافة جهة اتصال إلى قاعدة البيانات المحلية
  Future<bool> addContactToDatabase(String name, String phoneNumber, {String? email}) async {
    try {
      final contact = ContactModel(
        name: name,
        phoneNumber: phoneNumber,
        email: email,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _databaseService.insertContact(contact);
      return true;
    } catch (e) {
      print('Error adding contact to database: $e');
      return false;
    }
  }

  // البحث عن جهة اتصال في قاعدة البيانات المحلية
  Future<List<ContactModel>> searchLocalContacts(String query) async {
    try {
      return await _databaseService.searchContacts(query);
    } catch (e) {
      print('Error searching local contacts: $e');
      return [];
    }
  }

  // الحصول على جميع جهات الاتصال المحلية
  Future<List<ContactModel>> getAllLocalContacts() async {
    try {
      return await _databaseService.getAllContacts();
    } catch (e) {
      print('Error getting all local contacts: $e');
      return [];
    }
  }

  // البحث عن جهة اتصال بالاسم
  Future<ContactModel?> findContactByName(String name) async {
    try {
      final contacts = await _databaseService.searchContacts(name);
      if (contacts.isNotEmpty) {
        // البحث عن تطابق دقيق أولاً
        for (final contact in contacts) {
          if (contact.name.toLowerCase() == name.toLowerCase()) {
            return contact;
          }
        }
        // إذا لم نجد تطابق دقيق، نعيد أول نتيجة
        return contacts.first;
      }
      return null;
    } catch (e) {
      print('Error finding contact by name: $e');
      return null;
    }
  }

  // تحديث جهة اتصال
  Future<bool> updateContact(ContactModel contact) async {
    try {
      await _databaseService.updateContact(contact);
      return true;
    } catch (e) {
      print('Error updating contact: $e');
      return false;
    }
  }

  // حذف جهة اتصال
  Future<bool> deleteContact(int contactId) async {
    try {
      await _databaseService.deleteContact(contactId);
      return true;
    } catch (e) {
      print('Error deleting contact: $e');
      return false;
    }
  }

  // تعيين جهة اتصال كمفضلة
  Future<bool> toggleFavorite(int contactId) async {
    try {
      final contact = await _databaseService.getContactById(contactId);
      if (contact != null) {
        final updatedContact = contact.copyWith(
          isFavorite: !contact.isFavorite,
          updatedAt: DateTime.now(),
        );
        await _databaseService.updateContact(updatedContact);
        return true;
      }
      return false;
    } catch (e) {
      print('Error toggling favorite: $e');
      return false;
    }
  }

  // الحصول على جهات الاتصال المفضلة
  Future<List<ContactModel>> getFavoriteContacts() async {
    try {
      return await _databaseService.getFavoriteContacts();
    } catch (e) {
      print('Error getting favorite contacts: $e');
      return [];
    }
  }

  // مزامنة جهات الاتصال من الهاتف إلى قاعدة البيانات
  Future<bool> syncContactsFromDevice() async {
    try {
      final deviceContacts = await getDeviceContacts();
      
      for (final deviceContact in deviceContacts) {
        if (deviceContact.displayName != null && 
            deviceContact.phones != null && 
            deviceContact.phones!.isNotEmpty) {
          
          final name = deviceContact.displayName!;
          final phoneNumber = deviceContact.phones!.first.value ?? '';
          final email = deviceContact.emails?.isNotEmpty == true 
              ? deviceContact.emails!.first.value 
              : null;

          // التحقق من عدم وجود جهة الاتصال مسبقاً
          final existingContacts = await _databaseService.searchContacts(name);
          final exists = existingContacts.any((c) => 
              c.name.toLowerCase() == name.toLowerCase() && 
              c.phoneNumber == phoneNumber);

          if (!exists) {
            await addContactToDatabase(name, phoneNumber, email: email);
          }
        }
      }
      
      return true;
    } catch (e) {
      print('Error syncing contacts from device: $e');
      return false;
    }
  }

  // التحقق من صلاحيات الهاتف
  Future<bool> checkPhonePermissions() async {
    final phonePermission = await Permission.phone.status;
    final contactsPermission = await Permission.contacts.status;
    
    return phonePermission == PermissionStatus.granted && 
           contactsPermission == PermissionStatus.granted;
  }

  // طلب صلاحيات الهاتف
  Future<bool> requestPhonePermissions() async {
    final permissions = await [
      Permission.phone,
      Permission.contacts,
    ].request();

    return permissions.values.every((status) => status == PermissionStatus.granted);
  }

  // فتح تطبيق الهاتف
  Future<bool> openPhoneApp() async {
    try {
      if (Platform.isAndroid) {
        await _channel.invokeMethod('openPhoneApp');
        return true;
      } else {
        // لـ iOS
        final uri = Uri(scheme: 'tel', path: '');
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri);
          return true;
        }
      }
      return false;
    } catch (e) {
      print('Error opening phone app: $e');
      return false;
    }
  }

  // فتح تطبيق الرسائل
  Future<bool> openMessagesApp() async {
    try {
      if (Platform.isAndroid) {
        await _channel.invokeMethod('openMessagesApp');
        return true;
      } else {
        // لـ iOS
        final uri = Uri(scheme: 'sms', path: '');
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri);
          return true;
        }
      }
      return false;
    } catch (e) {
      print('Error opening messages app: $e');
      return false;
    }
  }
}
