<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Smart Talk</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>smart_talk</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>

	<!-- الصلاحيات المطلوبة للتطبيق -->
	<!-- صلاحية الوصول للميكروفون -->
	<key>NSMicrophoneUsageDescription</key>
	<string>يحتاج التطبيق للوصول للميكروفون لتسجيل الأوامر الصوتية وبصمة الصوت</string>

	<!-- صلاحية الوصول لجهات الاتصال -->
	<key>NSContactsUsageDescription</key>
	<string>يحتاج التطبيق للوصول لجهات الاتصال لإجراء المكالمات وإرسال الرسائل</string>

	<!-- صلاحية التعرف على الكلام -->
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>يحتاج التطبيق للتعرف على الكلام لفهم الأوامر الصوتية</string>

	<!-- صلاحية الكاميرا (اختيارية) -->
	<key>NSCameraUsageDescription</key>
	<string>يحتاج التطبيق للكاميرا لإضافة صور جهات الاتصال</string>

	<!-- صلاحية مكتبة الصور -->
	<key>NSPhotoLibraryUsageDescription</key>
	<string>يحتاج التطبيق لمكتبة الصور لإضافة صور جهات الاتصال</string>

	<!-- دعم فتح التطبيقات الأخرى -->
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>tel</string>
		<string>sms</string>
		<string>whatsapp</string>
		<string>youtube</string>
		<string>vnd.youtube</string>
	</array>

</dict>
</plist>
