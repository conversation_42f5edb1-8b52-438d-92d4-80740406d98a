import 'dart:io';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class YouTubeService {
  static final YouTubeService _instance = YouTubeService._internal();
  factory YouTubeService() => _instance;
  YouTubeService._internal();

  static const MethodChannel _channel = MethodChannel('smart_talk/youtube');
  
  // مفتاح API لـ YouTube (يجب الحصول عليه من Google Cloud Console)
  static const String _apiKey = 'YOUR_YOUTUBE_API_KEY';
  static const String _baseUrl = 'https://www.googleapis.com/youtube/v3';

  // فتح تطبيق YouTube
  Future<bool> openYouTube() async {
    try {
      if (Platform.isAndroid) {
        // محاولة فتح التطبيق مباشرة
        final uri = Uri.parse('vnd.youtube://');
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
          return true;
        }
      }
      
      // إذا فشل فتح التطبيق، افتح الموقع
      final webUri = Uri.parse('https://www.youtube.com');
      if (await canLaunchUrl(webUri)) {
        await launchUrl(webUri, mode: LaunchMode.externalApplication);
        return true;
      }
      
      return false;
    } catch (e) {
      print('Error opening YouTube: $e');
      return false;
    }
  }

  // البحث عن فيديو وتشغيله
  Future<bool> searchAndPlayVideo(String query) async {
    try {
      // البحث عن الفيديو
      final videoId = await _searchVideo(query);
      if (videoId != null) {
        return await _playVideo(videoId);
      }
      return false;
    } catch (e) {
      print('Error searching and playing video: $e');
      return false;
    }
  }

  // تشغيل فيديو بمعرف محدد
  Future<bool> playVideoById(String videoId) async {
    return await _playVideo(videoId);
  }

  // تشغيل فيديو
  Future<bool> _playVideo(String videoId) async {
    try {
      if (Platform.isAndroid) {
        // محاولة فتح الفيديو في تطبيق YouTube
        final appUri = Uri.parse('vnd.youtube://$videoId');
        if (await canLaunchUrl(appUri)) {
          await launchUrl(appUri, mode: LaunchMode.externalApplication);
          return true;
        }
      }
      
      // إذا فشل فتح التطبيق، افتح في المتصفح
      final webUri = Uri.parse('https://www.youtube.com/watch?v=$videoId');
      if (await canLaunchUrl(webUri)) {
        await launchUrl(webUri, mode: LaunchMode.externalApplication);
        return true;
      }
      
      return false;
    } catch (e) {
      print('Error playing video: $e');
      return false;
    }
  }

  // البحث عن فيديو باستخدام YouTube API
  Future<String?> _searchVideo(String query) async {
    try {
      if (_apiKey == 'YOUR_YOUTUBE_API_KEY') {
        // إذا لم يتم تعيين مفتاح API، استخدم البحث المباشر
        return await _searchVideoDirectly(query);
      }

      final url = Uri.parse(
        '$_baseUrl/search?part=snippet&q=${Uri.encodeComponent(query)}&type=video&maxResults=1&key=$_apiKey'
      );

      final response = await http.get(url);
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['items'] != null && data['items'].isNotEmpty) {
          return data['items'][0]['id']['videoId'];
        }
      }
      
      return null;
    } catch (e) {
      print('Error searching video: $e');
      return await _searchVideoDirectly(query);
    }
  }

  // البحث المباشر (بدون API)
  Future<String?> _searchVideoDirectly(String query) async {
    try {
      // فتح YouTube مع استعلام البحث
      final searchUri = Uri.parse(
        'https://www.youtube.com/results?search_query=${Uri.encodeComponent(query)}'
      );
      
      if (await canLaunchUrl(searchUri)) {
        await launchUrl(searchUri, mode: LaunchMode.externalApplication);
        return 'search_opened'; // إشارة إلى أن البحث تم فتحه
      }
      
      return null;
    } catch (e) {
      print('Error in direct search: $e');
      return null;
    }
  }

  // التحكم في التشغيل (يتطلب تطبيق مخصص أو استخدام platform channels)
  Future<bool> pauseVideo() async {
    try {
      if (Platform.isAndroid) {
        return await _channel.invokeMethod('pauseVideo') ?? false;
      }
      return false;
    } catch (e) {
      print('Error pausing video: $e');
      return false;
    }
  }

  Future<bool> resumeVideo() async {
    try {
      if (Platform.isAndroid) {
        return await _channel.invokeMethod('resumeVideo') ?? false;
      }
      return false;
    } catch (e) {
      print('Error resuming video: $e');
      return false;
    }
  }

  Future<bool> nextVideo() async {
    try {
      if (Platform.isAndroid) {
        return await _channel.invokeMethod('nextVideo') ?? false;
      }
      return false;
    } catch (e) {
      print('Error going to next video: $e');
      return false;
    }
  }

  Future<bool> previousVideo() async {
    try {
      if (Platform.isAndroid) {
        return await _channel.invokeMethod('previousVideo') ?? false;
      }
      return false;
    } catch (e) {
      print('Error going to previous video: $e');
      return false;
    }
  }

  Future<bool> toggleFullscreen() async {
    try {
      if (Platform.isAndroid) {
        return await _channel.invokeMethod('toggleFullscreen') ?? false;
      }
      return false;
    } catch (e) {
      print('Error toggling fullscreen: $e');
      return false;
    }
  }

  // فتح قناة معينة
  Future<bool> openChannel(String channelName) async {
    try {
      final searchUri = Uri.parse(
        'https://www.youtube.com/results?search_query=${Uri.encodeComponent(channelName)}&sp=EgIQAg%253D%253D'
      );
      
      if (await canLaunchUrl(searchUri)) {
        await launchUrl(searchUri, mode: LaunchMode.externalApplication);
        return true;
      }
      
      return false;
    } catch (e) {
      print('Error opening channel: $e');
      return false;
    }
  }

  // فتح قائمة تشغيل
  Future<bool> openPlaylist(String playlistName) async {
    try {
      final searchUri = Uri.parse(
        'https://www.youtube.com/results?search_query=${Uri.encodeComponent(playlistName)}&sp=EgIQAw%253D%253D'
      );
      
      if (await canLaunchUrl(searchUri)) {
        await launchUrl(searchUri, mode: LaunchMode.externalApplication);
        return true;
      }
      
      return false;
    } catch (e) {
      print('Error opening playlist: $e');
      return false;
    }
  }

  // الحصول على معلومات الفيديو
  Future<Map<String, dynamic>?> getVideoInfo(String videoId) async {
    try {
      if (_apiKey == 'YOUR_YOUTUBE_API_KEY') {
        return null;
      }

      final url = Uri.parse(
        '$_baseUrl/videos?part=snippet,statistics&id=$videoId&key=$_apiKey'
      );

      final response = await http.get(url);
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['items'] != null && data['items'].isNotEmpty) {
          return data['items'][0];
        }
      }
      
      return null;
    } catch (e) {
      print('Error getting video info: $e');
      return null;
    }
  }

  // البحث عن فيديوهات متعددة
  Future<List<Map<String, dynamic>>> searchVideos(String query, {int maxResults = 10}) async {
    try {
      if (_apiKey == 'YOUR_YOUTUBE_API_KEY') {
        return [];
      }

      final url = Uri.parse(
        '$_baseUrl/search?part=snippet&q=${Uri.encodeComponent(query)}&type=video&maxResults=$maxResults&key=$_apiKey'
      );

      final response = await http.get(url);
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['items'] != null) {
          return List<Map<String, dynamic>>.from(data['items']);
        }
      }
      
      return [];
    } catch (e) {
      print('Error searching videos: $e');
      return [];
    }
  }

  // فتح الفيديوهات الشائعة
  Future<bool> openTrending() async {
    try {
      final uri = Uri.parse('https://www.youtube.com/feed/trending');
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        return true;
      }
      return false;
    } catch (e) {
      print('Error opening trending: $e');
      return false;
    }
  }

  // فتح الاشتراكات
  Future<bool> openSubscriptions() async {
    try {
      final uri = Uri.parse('https://www.youtube.com/feed/subscriptions');
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        return true;
      }
      return false;
    } catch (e) {
      print('Error opening subscriptions: $e');
      return false;
    }
  }

  // فتح المكتبة
  Future<bool> openLibrary() async {
    try {
      final uri = Uri.parse('https://www.youtube.com/feed/library');
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        return true;
      }
      return false;
    } catch (e) {
      print('Error opening library: $e');
      return false;
    }
  }

  // التحقق من توفر تطبيق YouTube
  Future<bool> isYouTubeAppInstalled() async {
    try {
      if (Platform.isAndroid) {
        final uri = Uri.parse('vnd.youtube://');
        return await canLaunchUrl(uri);
      }
      return false;
    } catch (e) {
      print('Error checking YouTube app: $e');
      return false;
    }
  }
}
