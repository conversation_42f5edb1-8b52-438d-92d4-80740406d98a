import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:flutter_tts/flutter_tts.dart';
import 'package:record/record.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import '../utils/utils.dart';

class SpeechService {
  static final SpeechService _instance = SpeechService._internal();
  factory SpeechService() => _instance;
  SpeechService._internal();

  // Logger
  final Logger _logger = Logger();

  // Speech to Text
  late stt.SpeechToText _speechToText;
  bool _speechEnabled = false;
  String _lastWords = '';

  // Text to Speech
  late FlutterTts _flutterTts;

  // Audio Recording
  late AudioRecorder _audioRecorder;
  bool _isRecording = false;
  String? _currentRecordingPath;

  // Stream Controllers
  final StreamController<String> _speechResultController =
      StreamController<String>.broadcast();
  final StreamController<bool> _listeningController =
      StreamController<bool>.broadcast();
  final StreamController<double> _soundLevelController =
      StreamController<double>.broadcast();

  // Getters for streams
  Stream<String> get speechResultStream => _speechResultController.stream;
  Stream<bool> get listeningStream => _listeningController.stream;
  Stream<double> get soundLevelStream => _soundLevelController.stream;

  // Getters
  bool get isListening => _speechToText.isListening;
  bool get isRecording => _isRecording;
  bool get speechEnabled => _speechEnabled;
  String get lastWords => _lastWords;

  Future<void> initialize() async {
    await _initializeSpeechToText();
    await _initializeTextToSpeech();
    await _initializeAudioRecorder();
  }

  // تهيئة Speech to Text
  Future<void> _initializeSpeechToText() async {
    _speechToText = stt.SpeechToText();
    _speechEnabled = await _speechToText.initialize(
      onStatus: (status) {
        _logger.i('Speech status: $status');
        _listeningController.add(_speechToText.isListening);
      },
      onError: (error) {
        _logger.e('Speech error: $error');
        _listeningController.add(false);
      },
    );
  }

  // تهيئة Text to Speech
  Future<void> _initializeTextToSpeech() async {
    _flutterTts = FlutterTts();

    await _flutterTts.setLanguage("ar-SA"); // العربية السعودية
    await _flutterTts.setSpeechRate(0.5);
    await _flutterTts.setVolume(1.0);
    await _flutterTts.setPitch(1.0);

    // إعداد callbacks
    _flutterTts.setStartHandler(() {
      _logger.i("TTS Started");
    });

    _flutterTts.setCompletionHandler(() {
      _logger.i("TTS Completed");
    });

    _flutterTts.setErrorHandler((msg) {
      _logger.e("TTS Error: $msg");
    });
  }

  // تهيئة Audio Recorder
  Future<void> _initializeAudioRecorder() async {
    _audioRecorder = AudioRecorder();
  }

  // بدء الاستماع للكلام
  Future<void> startListening({
    String localeId = 'ar-SA',
    Duration? timeout,
  }) async {
    if (!_speechEnabled) {
      _logger.e('Speech recognition not available');
      return;
    }

    // طلب إذن الميكروفون
    final permission = await Permission.microphone.request();
    if (permission != PermissionStatus.granted) {
      throw Exception('Microphone permission not granted');
    }

    await _speechToText.listen(
      onResult: (result) {
        _lastWords = result.recognizedWords;
        _speechResultController.add(_lastWords);
        _logger.i('Speech result: $_lastWords');
      },
      listenFor: timeout ?? const Duration(seconds: 30),
      pauseFor: const Duration(seconds: 3),
      localeId: localeId,
      onSoundLevelChange: (level) {
        _soundLevelController.add(level);
      },
      listenOptions: stt.SpeechListenOptions(partialResults: true),
    );
  }

  // إيقاف الاستماع
  Future<void> stopListening() async {
    await _speechToText.stop();
    _listeningController.add(false);
  }

  // إلغاء الاستماع
  Future<void> cancelListening() async {
    await _speechToText.cancel();
    _listeningController.add(false);
  }

  // تحويل النص إلى كلام
  Future<void> speak(String text, {String? language}) async {
    if (language != null) {
      await _flutterTts.setLanguage(language);
    }

    await _flutterTts.speak(text);
  }

  // إيقاف التحدث
  Future<void> stopSpeaking() async {
    await _flutterTts.stop();
  }

  // بدء تسجيل الصوت
  Future<String?> startRecording() async {
    try {
      if (kIsWeb) {
        _logger.w('Recording is not fully supported in web environment');
        return null;
      }

      // طلب إذن الميكروفون
      final permission = await Permission.microphone.request();
      if (permission != PermissionStatus.granted) {
        throw Exception('Microphone permission not granted');
      }

      // إنشاء مسار الملف
      String filePath;
      if (kIsWeb) {
        // في بيئة الويب، نستخدم مسار افتراضي
        final fileName =
            'recording_${DateTime.now().millisecondsSinceEpoch}.m4a';
        filePath = fileName;
      } else {
        // في بيئة التطبيق العادية، نستخدم مجلد المستندات
        final directory = await getApplicationDocumentsDirectory();
        final fileName =
            'recording_${DateTime.now().millisecondsSinceEpoch}.m4a';
        filePath = '${directory.path}/$fileName';
      }
      _currentRecordingPath = filePath;

      // بدء التسجيل
      await _audioRecorder.start(
        const RecordConfig(
          encoder: AudioEncoder.aacLc,
          bitRate: 128000,
          sampleRate: 44100,
        ),
        path: _currentRecordingPath!,
      );

      _isRecording = true;
      return _currentRecordingPath;
    } catch (e) {
      _logger.e('Error starting recording: $e');
      return null;
    }
  }

  // إيقاف تسجيل الصوت
  Future<String?> stopRecording() async {
    try {
      if (kIsWeb) {
        _logger
            .w('Recording stopping is not fully supported in web environment');
        _isRecording = false;
        return _currentRecordingPath;
      }

      final path = await _audioRecorder.stop();
      _isRecording = false;
      return path;
    } catch (e) {
      _logger.e('Error stopping recording: $e');
      return null;
    }
  }

  // إلغاء تسجيل الصوت
  Future<void> cancelRecording() async {
    try {
      if (kIsWeb) {
        _logger.w(
            'Recording cancellation is not fully supported in web environment');
        _isRecording = false;
        _currentRecordingPath = null;
        return;
      }

      await _audioRecorder.stop();
      _isRecording = false;

      // حذف الملف إذا كان موجوداً
      if (_currentRecordingPath != null && !kIsWeb) {
        final file = File(_currentRecordingPath!);
        if (await file.exists()) {
          await file.delete();
        }
      }
      _currentRecordingPath = null;
    } catch (e) {
      _logger.e('Error cancelling recording: $e');
    }
  }

  // الحصول على اللغات المتاحة للـ Speech to Text
  Future<List<stt.LocaleName>> getAvailableLocales() async {
    return await _speechToText.locales();
  }

  // الحصول على اللغات المتاحة للـ Text to Speech
  Future<List<dynamic>> getAvailableTtsLanguages() async {
    return await _flutterTts.getLanguages;
  }

  // تعيين لغة Speech to Text
  Future<void> setSpeechLanguage(String localeId) async {
    // سيتم استخدامها في startListening
  }

  // تعيين لغة Text to Speech
  Future<void> setTtsLanguage(String language) async {
    await _flutterTts.setLanguage(language);
  }

  // تعيين سرعة التحدث
  Future<void> setSpeechRate(double rate) async {
    await _flutterTts.setSpeechRate(rate);
  }

  // تعيين مستوى الصوت
  Future<void> setVolume(double volume) async {
    await _flutterTts.setVolume(volume);
  }

  // تعيين نبرة الصوت
  Future<void> setPitch(double pitch) async {
    await _flutterTts.setPitch(pitch);
  }

  // تنظيف الموارد
  void dispose() {
    _speechResultController.close();
    _listeningController.close();
    _soundLevelController.close();
    _audioRecorder.dispose();
  }
}
