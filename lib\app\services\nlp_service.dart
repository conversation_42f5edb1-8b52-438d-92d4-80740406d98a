import '../models/models.dart';

class NLPService {
  static final NLPService _instance = NLPService._internal();
  factory NLPService() => _instance;
  NLPService._internal();

  // تحليل النص واستخراج النية والكيانات
  Future<CommandAnalysis> analyzeCommand(String text) async {
    final cleanText = _cleanText(text);
    final intent = _extractIntent(cleanText);
    final entities = _extractEntities(cleanText, intent);
    final commandType = _getCommandType(intent);
    final confidence = _calculateConfidence(cleanText, intent);

    return CommandAnalysis(
      originalText: text,
      cleanText: cleanText,
      intent: intent,
      entities: entities,
      commandType: commandType,
      confidence: confidence,
    );
  }

  // تنظيف النص
  String _cleanText(String text) {
    return text
        .toLowerCase()
        .trim()
        .replaceAll(RegExp(r'[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFFa-zA-Z0-9\s]'), '')
        .replaceAll(RegExp(r'\s+'), ' ');
  }

  // استخراج النية من النص
  Intent _extractIntent(String text) {
    // أنماط الاتصال
    if (_matchesPatterns(text, [
      r'اتصل\s+ب',
      r'كلم',
      r'اتصال',
      r'call',
      r'phone',
    ])) {
      return Intent.makeCall;
    }

    // أنماط الرسائل النصية
    if (_matchesPatterns(text, [
      r'أرسل\s+رسالة',
      r'ابعث\s+رسالة',
      r'رسالة\s+نصية',
      r'send\s+sms',
      r'text\s+message',
    ])) {
      return Intent.sendSms;
    }

    // أنماط الواتساب
    if (_matchesPatterns(text, [
      r'أرسل\s+واتساب',
      r'ابعث\s+واتساب',
      r'واتس\s+اب',
      r'whatsapp',
      r'send\s+whatsapp',
    ])) {
      return Intent.sendWhatsapp;
    }

    // أنماط فتح يوتيوب
    if (_matchesPatterns(text, [
      r'افتح\s+يوتيوب',
      r'شغل\s+يوتيوب',
      r'open\s+youtube',
      r'يوتيوب',
      r'youtube',
    ])) {
      return Intent.openYoutube;
    }

    // أنماط تشغيل فيديو
    if (_matchesPatterns(text, [
      r'شغل\s+فيديو',
      r'ابحث\s+عن',
      r'play\s+video',
      r'search\s+for',
      r'شغل',
    ])) {
      return Intent.playVideo;
    }

    // أنماط إيقاف مؤقت
    if (_matchesPatterns(text, [
      r'أوقف',
      r'توقف',
      r'pause',
      r'stop',
    ])) {
      return Intent.pauseVideo;
    }

    // أنماط التالي
    if (_matchesPatterns(text, [
      r'التالي',
      r'الفيديو\s+التالي',
      r'next',
      r'next\s+video',
    ])) {
      return Intent.nextVideo;
    }

    // أنماط السابق
    if (_matchesPatterns(text, [
      r'السابق',
      r'الفيديو\s+السابق',
      r'previous',
      r'previous\s+video',
    ])) {
      return Intent.previousVideo;
    }

    // أنماط إضافة جهة اتصال
    if (_matchesPatterns(text, [
      r'أضف\s+جهة\s+اتصال',
      r'احفظ\s+رقم',
      r'add\s+contact',
      r'save\s+number',
    ])) {
      return Intent.addContact;
    }

    // أنماط البحث عن جهة اتصال
    if (_matchesPatterns(text, [
      r'ابحث\s+عن',
      r'أين\s+رقم',
      r'search\s+contact',
      r'find\s+contact',
    ])) {
      return Intent.searchContact;
    }

    return Intent.unknown;
  }

  // استخراج الكيانات من النص
  Map<String, dynamic> _extractEntities(String text, Intent intent) {
    Map<String, dynamic> entities = {};

    switch (intent) {
      case Intent.makeCall:
      case Intent.sendSms:
      case Intent.sendWhatsapp:
        entities['contact_name'] = _extractContactName(text);
        entities['phone_number'] = _extractPhoneNumber(text);
        if (intent == Intent.sendSms || intent == Intent.sendWhatsapp) {
          entities['message'] = _extractMessage(text);
        }
        break;

      case Intent.playVideo:
        entities['video_query'] = _extractVideoQuery(text);
        break;

      case Intent.addContact:
        entities['contact_name'] = _extractContactName(text);
        entities['phone_number'] = _extractPhoneNumber(text);
        entities['email'] = _extractEmail(text);
        break;

      case Intent.searchContact:
        entities['search_query'] = _extractSearchQuery(text);
        break;

      default:
        break;
    }

    return entities;
  }

  // استخراج اسم جهة الاتصال
  String? _extractContactName(String text) {
    // البحث عن أنماط مثل "اتصل بـ أحمد" أو "أرسل رسالة إلى سارة"
    final patterns = [
      r'(?:اتصل\s+ب|كلم|أرسل.*?إلى|ابعث.*?إلى)\s+([^\s]+)',
      r'(?:call|text|send.*?to)\s+([^\s]+)',
    ];

    for (final pattern in patterns) {
      final match = RegExp(pattern, caseSensitive: false).firstMatch(text);
      if (match != null) {
        return match.group(1);
      }
    }

    return null;
  }

  // استخراج رقم الهاتف
  String? _extractPhoneNumber(text) {
    final phonePattern = RegExp(r'(\+?[0-9]{10,15})');
    final match = phonePattern.firstMatch(text);
    return match?.group(1);
  }

  // استخراج الرسالة
  String? _extractMessage(String text) {
    // البحث عن النص بعد كلمات مثل "قل له" أو "اكتب"
    final patterns = [
      r'(?:قل\s+له|اكتب|الرسالة)\s+(.+)',
      r'(?:say|write|message)\s+(.+)',
    ];

    for (final pattern in patterns) {
      final match = RegExp(pattern, caseSensitive: false).firstMatch(text);
      if (match != null) {
        return match.group(1);
      }
    }

    return null;
  }

  // استخراج استعلام الفيديو
  String? _extractVideoQuery(String text) {
    // البحث عن النص بعد كلمات مثل "شغل" أو "ابحث عن"
    final patterns = [
      r'(?:شغل|ابحث\s+عن|play|search\s+for)\s+(.+)',
    ];

    for (final pattern in patterns) {
      final match = RegExp(pattern, caseSensitive: false).firstMatch(text);
      if (match != null) {
        return match.group(1);
      }
    }

    return text; // إذا لم نجد نمط محدد، نعيد النص كاملاً
  }

  // استخراج البريد الإلكتروني
  String? _extractEmail(String text) {
    final emailPattern = RegExp(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b');
    final match = emailPattern.firstMatch(text);
    return match?.group(0);
  }

  // استخراج استعلام البحث
  String? _extractSearchQuery(String text) {
    final patterns = [
      r'(?:ابحث\s+عن|أين|search\s+for|find)\s+(.+)',
    ];

    for (final pattern in patterns) {
      final match = RegExp(pattern, caseSensitive: false).firstMatch(text);
      if (match != null) {
        return match.group(1);
      }
    }

    return null;
  }

  // تحديد نوع الأمر
  CommandType _getCommandType(Intent intent) {
    switch (intent) {
      case Intent.makeCall:
        return CommandType.call;
      case Intent.sendSms:
        return CommandType.sms;
      case Intent.sendWhatsapp:
        return CommandType.whatsapp;
      case Intent.openYoutube:
      case Intent.playVideo:
      case Intent.pauseVideo:
      case Intent.nextVideo:
      case Intent.previousVideo:
        return CommandType.youtube;
      case Intent.addContact:
      case Intent.searchContact:
        return CommandType.contact;
      default:
        return CommandType.unknown;
    }
  }

  // حساب مستوى الثقة
  double _calculateConfidence(String text, Intent intent) {
    if (intent == Intent.unknown) return 0.0;

    double confidence = 0.5; // قيمة أساسية

    // زيادة الثقة بناءً على وضوح الأمر
    if (text.length > 5) confidence += 0.1;
    if (text.split(' ').length > 2) confidence += 0.1;

    // زيادة الثقة بناءً على وجود كلمات مفتاحية قوية
    final strongKeywords = {
      Intent.makeCall: ['اتصل', 'كلم', 'call'],
      Intent.sendSms: ['رسالة', 'ابعث', 'sms'],
      Intent.sendWhatsapp: ['واتساب', 'whatsapp'],
      Intent.openYoutube: ['يوتيوب', 'youtube'],
      Intent.playVideo: ['شغل', 'play'],
    };

    final keywords = strongKeywords[intent] ?? [];
    for (final keyword in keywords) {
      if (text.contains(keyword)) {
        confidence += 0.2;
        break;
      }
    }

    return confidence.clamp(0.0, 1.0);
  }

  // فحص تطابق الأنماط
  bool _matchesPatterns(String text, List<String> patterns) {
    for (final pattern in patterns) {
      if (RegExp(pattern, caseSensitive: false).hasMatch(text)) {
        return true;
      }
    }
    return false;
  }
}

// فئة لنتيجة تحليل الأمر
class CommandAnalysis {
  final String originalText;
  final String cleanText;
  final Intent intent;
  final Map<String, dynamic> entities;
  final CommandType commandType;
  final double confidence;

  CommandAnalysis({
    required this.originalText,
    required this.cleanText,
    required this.intent,
    required this.entities,
    required this.commandType,
    required this.confidence,
  });

  @override
  String toString() {
    return 'CommandAnalysis{intent: $intent, commandType: $commandType, confidence: $confidence, entities: $entities}';
  }
}
