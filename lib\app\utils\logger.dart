import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';

// خدمة تسجيل بسيطة للتطبيق
class Logger {
  static final Logger _instance = Logger._internal();
  factory Logger() => _instance;
  Logger._internal();

  // مستويات التسجيل
  static const int VERBOSE = 0;
  static const int DEBUG = 1;
  static const int INFO = 2;
  static const int WARNING = 3;
  static const int ERROR = 4;
  static const int NONE = 5;

  // المستوى الحالي للتسجيل (يمكن تغييره حسب البيئة)
  int _currentLevel = kReleaseMode ? INFO : DEBUG;

  // حفظ السجلات في ملف
  bool _logToFile = false;
  String _logFilePath = '';

  // تهيئة ملف السجلات
  Future<void> initLogFile() async {
    try {
      if (!kIsWeb) {
        try {
          final directory = await getApplicationDocumentsDirectory();
          final String date = DateTime.now().toIso8601String().split('T')[0];
          _logFilePath = '${directory.path}/logs_$date.txt';
          _logToFile = true;
        } catch (e) {
          print('Error getting application documents directory: $e');
          _logToFile = false;
        }
      } else {
        // في بيئة الويب، نعطل تسجيل الملفات
        _logToFile = false;
        print('Log file disabled in web environment');
      }
    } catch (e) {
      print('Error initializing log file: $e');
      _logToFile = false;
    }
  }

  // تعيين مستوى التسجيل
  void setLogLevel(int level) {
    _currentLevel = level;
  }

  // طباعة رسالة تسجيل
  void _log(String message, int level, String prefix) async {
    if (level >= _currentLevel) {
      final DateTime now = DateTime.now();
      final String timeString =
          "${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}.${now.millisecond.toString().padLeft(3, '0')}";
      final String logMessage = "[$timeString] $prefix: $message";

      // طباعة في وحدة التحكم
      print(logMessage);

      // حفظ في ملف إذا كان مفعلاً
      if (_logToFile && _logFilePath.isNotEmpty && !kIsWeb) {
        try {
          final file = File(_logFilePath);
          await file.writeAsString('$logMessage\n', mode: FileMode.append);
        } catch (e) {
          print('Error writing to log file: $e');
        }
      }
    }
  }

  // تسجيل رسالة تفصيلية
  void v(String message) {
    _log(message, VERBOSE, "VERBOSE");
  }

  // تسجيل رسالة تصحيح
  void d(String message) {
    _log(message, DEBUG, "DEBUG");
  }

  // تسجيل رسالة معلومات
  void i(String message) {
    _log(message, INFO, "INFO");
  }

  // تسجيل رسالة تحذير
  void w(String message) {
    _log(message, WARNING, "WARNING");
  }

  // تسجيل رسالة خطأ
  void e(String message) {
    _log(message, ERROR, "ERROR");
  }
}

// مثال للاستخدام:
// final logger = Logger();
// logger.d("هذه رسالة تصحيح");
// logger.e("حدث خطأ: $error");