package com.example.smart_talk

import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterActivity() {
    private val CHANNEL = "smart_talk/youtube"
    private val PHONE_CHANNEL = "smart_talk/phone"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // YouTube Channel
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "pauseVideo" -> {
                    val success = pauseYouTubeVideo()
                    result.success(success)
                }
                "resumeVideo" -> {
                    val success = resumeYouTubeVideo()
                    result.success(success)
                }
                "nextVideo" -> {
                    val success = nextYouTubeVideo()
                    result.success(success)
                }
                "previousVideo" -> {
                    val success = previousYouTubeVideo()
                    result.success(success)
                }
                "toggleFullscreen" -> {
                    val success = toggleYouTubeFullscreen()
                    result.success(success)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }

        // Phone Channel
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, PHONE_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "openPhoneApp" -> {
                    val success = openPhoneApp()
                    result.success(success)
                }
                "openMessagesApp" -> {
                    val success = openMessagesApp()
                    result.success(success)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    private fun pauseYouTubeVideo(): Boolean {
        return try {
            // إرسال أمر الإيقاف المؤقت لـ YouTube
            val intent = Intent("com.google.android.youtube.api.service.START")
            intent.putExtra("command", "pause")
            sendBroadcast(intent)
            true
        } catch (e: Exception) {
            false
        }
    }

    private fun resumeYouTubeVideo(): Boolean {
        return try {
            // إرسال أمر الاستئناف لـ YouTube
            val intent = Intent("com.google.android.youtube.api.service.START")
            intent.putExtra("command", "play")
            sendBroadcast(intent)
            true
        } catch (e: Exception) {
            false
        }
    }

    private fun nextYouTubeVideo(): Boolean {
        return try {
            // إرسال أمر الفيديو التالي لـ YouTube
            val intent = Intent("com.google.android.youtube.api.service.START")
            intent.putExtra("command", "next")
            sendBroadcast(intent)
            true
        } catch (e: Exception) {
            false
        }
    }

    private fun previousYouTubeVideo(): Boolean {
        return try {
            // إرسال أمر الفيديو السابق لـ YouTube
            val intent = Intent("com.google.android.youtube.api.service.START")
            intent.putExtra("command", "previous")
            sendBroadcast(intent)
            true
        } catch (e: Exception) {
            false
        }
    }

    private fun toggleYouTubeFullscreen(): Boolean {
        return try {
            // إرسال أمر تبديل ملء الشاشة لـ YouTube
            val intent = Intent("com.google.android.youtube.api.service.START")
            intent.putExtra("command", "fullscreen")
            sendBroadcast(intent)
            true
        } catch (e: Exception) {
            false
        }
    }

    private fun openPhoneApp(): Boolean {
        return try {
            val intent = Intent(Intent.ACTION_DIAL)
            startActivity(intent)
            true
        } catch (e: Exception) {
            false
        }
    }

    private fun openMessagesApp(): Boolean {
        return try {
            val intent = Intent(Intent.ACTION_MAIN)
            intent.addCategory(Intent.CATEGORY_APP_MESSAGING)
            startActivity(intent)
            true
        } catch (e: Exception) {
            // محاولة بديلة
            try {
                val smsIntent = Intent(Intent.ACTION_VIEW)
                smsIntent.data = Uri.parse("sms:")
                startActivity(smsIntent)
                true
            } catch (e2: Exception) {
                false
            }
        }
    }

    private fun isAppInstalled(packageName: String): Boolean {
        return try {
            packageManager.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }
}
