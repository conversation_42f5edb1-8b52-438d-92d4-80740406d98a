import 'package:get/get.dart';
import 'package:flutter/material.dart';
import '../models/models.dart';
import '../services/services.dart';

class SettingsController extends GetxController {
  // الخدمات
  final DatabaseService _databaseService = DatabaseService();

  // المتغيرات التفاعلية
  final RxString currentLanguage = 'ar'.obs;
  final RxString currentTheme = 'light'.obs;
  final RxBool isDarkMode = false.obs;
  final RxBool enableVoiceAuth = true.obs;
  final RxBool enableNotifications = true.obs;
  final RxBool enableActivityLogging = true.obs;
  final RxDouble voiceConfidenceThreshold = 0.8.obs;
  final RxString speechLanguage = 'ar-SA'.obs;
  final RxString ttsLanguage = 'ar-SA'.obs;
  final RxDouble speechRate = 0.5.obs;
  final RxDouble volume = 1.0.obs;
  final RxDouble pitch = 1.0.obs;
  final RxInt maxRecordingDuration = 30.obs;
  final RxString recordingQuality = 'high'.obs;
  final RxBool isLoading = false.obs;
  final RxString statusMessage = ''.obs;

  // معلومات المستخدم
  final Rx<UserModel?> currentUser = Rx<UserModel?>(null);

  @override
  void onInit() {
    super.onInit();
    _loadSettings();
  }

  // تحميل الإعدادات
  Future<void> _loadSettings() async {
    try {
      isLoading.value = true;
      
      // تحميل المستخدم الحالي
      final user = await _databaseService.getFirstUser();
      if (user != null) {
        currentUser.value = user;
        currentLanguage.value = user.language;
        currentTheme.value = user.themeMode;
        isDarkMode.value = user.themeMode == 'dark';
        
        // تحميل الإعدادات من قاعدة البيانات
        await _loadUserSettings(user.id!);
      }
    } catch (e) {
      statusMessage.value = 'خطأ في تحميل الإعدادات: $e';
    } finally {
      isLoading.value = false;
    }
  }

  // تحميل إعدادات المستخدم
  Future<void> _loadUserSettings(int userId) async {
    try {
      final settings = await _databaseService.getAllSettings(userId);
      
      for (final setting in settings) {
        switch (setting.settingKey) {
          case SettingKeys.enableVoiceAuthentication:
            enableVoiceAuth.value = setting.settingValue == 'true';
            break;
          case SettingKeys.enableNotifications:
            enableNotifications.value = setting.settingValue == 'true';
            break;
          case SettingKeys.enableActivityLogging:
            enableActivityLogging.value = setting.settingValue == 'true';
            break;
          case SettingKeys.voiceConfidenceThreshold:
            voiceConfidenceThreshold.value = double.parse(setting.settingValue);
            break;
          case SettingKeys.speechToTextLanguage:
            speechLanguage.value = setting.settingValue;
            break;
          case SettingKeys.textToSpeechLanguage:
            ttsLanguage.value = setting.settingValue;
            break;
          case SettingKeys.maxRecordingDuration:
            maxRecordingDuration.value = int.parse(setting.settingValue);
            break;
          case SettingKeys.recordingQuality:
            recordingQuality.value = setting.settingValue;
            break;
        }
      }
    } catch (e) {
      print('Error loading user settings: $e');
    }
  }

  // تغيير اللغة
  Future<void> changeLanguage(String languageCode) async {
    try {
      currentLanguage.value = languageCode;
      
      // تحديث اللغة في GetX
      final locale = languageCode == 'ar' 
          ? const Locale('ar', 'SA') 
          : const Locale('en', 'US');
      Get.updateLocale(locale);
      
      // حفظ في قاعدة البيانات
      await _updateUserLanguage(languageCode);
      
      statusMessage.value = 'تم تغيير اللغة بنجاح';
    } catch (e) {
      statusMessage.value = 'خطأ في تغيير اللغة: $e';
    }
  }

  // تغيير الثيم
  Future<void> changeTheme(String themeMode) async {
    try {
      currentTheme.value = themeMode;
      isDarkMode.value = themeMode == 'dark';
      
      // تحديث الثيم في GetX
      Get.changeThemeMode(
        themeMode == 'dark' ? ThemeMode.dark : ThemeMode.light
      );
      
      // حفظ في قاعدة البيانات
      await _updateUserTheme(themeMode);
      
      statusMessage.value = 'تم تغيير المظهر بنجاح';
    } catch (e) {
      statusMessage.value = 'خطأ في تغيير المظهر: $e';
    }
  }

  // تبديل الوضع الليلي
  Future<void> toggleDarkMode() async {
    final newTheme = isDarkMode.value ? 'light' : 'dark';
    await changeTheme(newTheme);
  }

  // تحديث إعداد صوتي
  Future<void> updateVoiceSetting(String key, dynamic value) async {
    try {
      if (currentUser.value == null) return;

      final setting = SettingModel(
        userId: currentUser.value!.id!,
        settingKey: key,
        settingValue: value.toString(),
        settingType: _getSettingType(value),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _databaseService.upsertSetting(setting);
      
      // تحديث القيمة المحلية
      switch (key) {
        case SettingKeys.enableVoiceAuthentication:
          enableVoiceAuth.value = value as bool;
          break;
        case SettingKeys.voiceConfidenceThreshold:
          voiceConfidenceThreshold.value = value as double;
          break;
        case SettingKeys.speechToTextLanguage:
          speechLanguage.value = value as String;
          break;
        case SettingKeys.textToSpeechLanguage:
          ttsLanguage.value = value as String;
          break;
        case SettingKeys.maxRecordingDuration:
          maxRecordingDuration.value = value as int;
          break;
        case SettingKeys.recordingQuality:
          recordingQuality.value = value as String;
          break;
      }
      
      statusMessage.value = 'تم حفظ الإعداد بنجاح';
    } catch (e) {
      statusMessage.value = 'خطأ في حفظ الإعداد: $e';
    }
  }

  // تحديث إعداد عام
  Future<void> updateGeneralSetting(String key, dynamic value) async {
    try {
      if (currentUser.value == null) return;

      final setting = SettingModel(
        userId: currentUser.value!.id!,
        settingKey: key,
        settingValue: value.toString(),
        settingType: _getSettingType(value),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _databaseService.upsertSetting(setting);
      
      // تحديث القيمة المحلية
      switch (key) {
        case SettingKeys.enableNotifications:
          enableNotifications.value = value as bool;
          break;
        case SettingKeys.enableActivityLogging:
          enableActivityLogging.value = value as bool;
          break;
      }
      
      statusMessage.value = 'تم حفظ الإعداد بنجاح';
    } catch (e) {
      statusMessage.value = 'خطأ في حفظ الإعداد: $e';
    }
  }

  // تحديث لغة المستخدم
  Future<void> _updateUserLanguage(String language) async {
    try {
      if (currentUser.value != null) {
        final updatedUser = currentUser.value!.copyWith(
          language: language,
          updatedAt: DateTime.now(),
        );
        
        await _databaseService.updateUser(updatedUser);
        currentUser.value = updatedUser;
      }
    } catch (e) {
      print('Error updating user language: $e');
    }
  }

  // تحديث ثيم المستخدم
  Future<void> _updateUserTheme(String themeMode) async {
    try {
      if (currentUser.value != null) {
        final updatedUser = currentUser.value!.copyWith(
          themeMode: themeMode,
          updatedAt: DateTime.now(),
        );
        
        await _databaseService.updateUser(updatedUser);
        currentUser.value = updatedUser;
      }
    } catch (e) {
      print('Error updating user theme: $e');
    }
  }

  // تحديد نوع الإعداد
  String _getSettingType(dynamic value) {
    if (value is bool) return 'boolean';
    if (value is int) return 'integer';
    if (value is double) return 'double';
    return 'string';
  }

  // إعادة تعيين الإعدادات
  Future<void> resetSettings() async {
    try {
      isLoading.value = true;
      statusMessage.value = 'جاري إعادة تعيين الإعدادات...';

      if (currentUser.value != null) {
        // حذف جميع الإعدادات
        final settings = await _databaseService.getAllSettings(currentUser.value!.id!);
        for (final setting in settings) {
          await _databaseService.deleteSetting(currentUser.value!.id!, setting.settingKey);
        }
        
        // إعادة تعيين القيم الافتراضية
        await _setDefaultSettings();
        
        statusMessage.value = 'تم إعادة تعيين الإعدادات بنجاح';
      }
    } catch (e) {
      statusMessage.value = 'خطأ في إعادة تعيين الإعدادات: $e';
    } finally {
      isLoading.value = false;
    }
  }

  // تعيين الإعدادات الافتراضية
  Future<void> _setDefaultSettings() async {
    enableVoiceAuth.value = true;
    enableNotifications.value = true;
    enableActivityLogging.value = true;
    voiceConfidenceThreshold.value = 0.8;
    speechLanguage.value = 'ar-SA';
    ttsLanguage.value = 'ar-SA';
    speechRate.value = 0.5;
    volume.value = 1.0;
    pitch.value = 1.0;
    maxRecordingDuration.value = 30;
    recordingQuality.value = 'high';
    
    // حفظ الإعدادات الافتراضية
    if (currentUser.value != null) {
      final defaultSettings = [
        SettingModel(
          userId: currentUser.value!.id!,
          settingKey: SettingKeys.enableVoiceAuthentication,
          settingValue: 'true',
          settingType: 'boolean',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        SettingModel(
          userId: currentUser.value!.id!,
          settingKey: SettingKeys.enableNotifications,
          settingValue: 'true',
          settingType: 'boolean',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        SettingModel(
          userId: currentUser.value!.id!,
          settingKey: SettingKeys.voiceConfidenceThreshold,
          settingValue: '0.8',
          settingType: 'double',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];
      
      for (final setting in defaultSettings) {
        await _databaseService.insertSetting(setting);
      }
    }
  }

  // مسح بيانات التطبيق
  Future<void> clearAppData() async {
    try {
      isLoading.value = true;
      statusMessage.value = 'جاري مسح بيانات التطبيق...';

      // حذف قاعدة البيانات
      await DatabaseHelper().deleteDatabase();
      
      // إعادة تهيئة المتغيرات
      currentUser.value = null;
      await _setDefaultSettings();
      
      statusMessage.value = 'تم مسح بيانات التطبيق بنجاح';
      
      // إعادة تشغيل التطبيق
      Get.offAllNamed('/');
    } catch (e) {
      statusMessage.value = 'خطأ في مسح بيانات التطبيق: $e';
    } finally {
      isLoading.value = false;
    }
  }

  // تصدير الإعدادات
  Future<Map<String, dynamic>> exportSettings() async {
    try {
      if (currentUser.value == null) return {};

      final settings = await _databaseService.getAllSettings(currentUser.value!.id!);
      final exportData = <String, dynamic>{};
      
      for (final setting in settings) {
        exportData[setting.settingKey] = setting.settingValue;
      }
      
      exportData['user_info'] = {
        'name': currentUser.value!.name,
        'language': currentUser.value!.language,
        'theme_mode': currentUser.value!.themeMode,
      };
      
      return exportData;
    } catch (e) {
      statusMessage.value = 'خطأ في تصدير الإعدادات: $e';
      return {};
    }
  }

  // استيراد الإعدادات
  Future<void> importSettings(Map<String, dynamic> settingsData) async {
    try {
      isLoading.value = true;
      statusMessage.value = 'جاري استيراد الإعدادات...';

      if (currentUser.value == null) return;

      // استيراد إعدادات المستخدم
      if (settingsData.containsKey('user_info')) {
        final userInfo = settingsData['user_info'] as Map<String, dynamic>;
        if (userInfo.containsKey('language')) {
          await changeLanguage(userInfo['language']);
        }
        if (userInfo.containsKey('theme_mode')) {
          await changeTheme(userInfo['theme_mode']);
        }
      }

      // استيراد باقي الإعدادات
      for (final entry in settingsData.entries) {
        if (entry.key != 'user_info') {
          final setting = SettingModel(
            userId: currentUser.value!.id!,
            settingKey: entry.key,
            settingValue: entry.value.toString(),
            settingType: _getSettingType(entry.value),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );
          
          await _databaseService.upsertSetting(setting);
        }
      }

      // إعادة تحميل الإعدادات
      await _loadUserSettings(currentUser.value!.id!);
      
      statusMessage.value = 'تم استيراد الإعدادات بنجاح';
    } catch (e) {
      statusMessage.value = 'خطأ في استيراد الإعدادات: $e';
    } finally {
      isLoading.value = false;
    }
  }

  // الحصول على معلومات التطبيق
  Map<String, String> getAppInfo() {
    return {
      'اسم التطبيق': 'Smart Talk',
      'الإصدار': '1.0.0',
      'المطور': 'Smart Talk Team',
      'تاريخ الإصدار': '2025-01-01',
    };
  }
}
