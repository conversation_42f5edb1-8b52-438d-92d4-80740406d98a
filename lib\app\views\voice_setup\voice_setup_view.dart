import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/controllers.dart';
import '../widgets/widgets.dart';

class VoiceSetupView extends GetView<VoiceController> {
  const VoiceSetupView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعداد بصمة الصوت'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: Obx(() => LoadingOverlay(
        isLoading: controller.isProcessing.value,
        loadingMessage: 'جاري معالجة بصمة الصوت...',
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // بطاقة المعلومات
              _buildInfoCard(),
              
              const SizedBox(height: 20),
              
              // حالة بصمة الصوت
              _buildVoicePrintStatus(),
              
              const SizedBox(height: 20),
              
              // منطقة التسجيل
              _buildRecordingArea(),
              
              const SizedBox(height: 20),
              
              // الإعدادات المتقدمة
              _buildAdvancedSettings(),
              
              const SizedBox(height: 20),
              
              // الأزرار
              _buildActionButtons(),
            ],
          ),
        ),
      )),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Icon(
              Icons.security,
              size: 48,
              color: Get.theme.colorScheme.primary,
            ),
            const SizedBox(height: 12),
            Text(
              'بصمة الصوت للأمان',
              style: Get.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'تساعد بصمة الصوت في التأكد من هويتك قبل تنفيذ الأوامر الصوتية، مما يضمن عدم تنفيذ أوامر من أشخاص آخرين.',
              style: Get.textTheme.bodyMedium?.copyWith(
                color: Get.theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVoicePrintStatus() {
    return Obx(() => Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Icon(
              controller.hasVoicePrint.value 
                  ? Icons.check_circle 
                  : Icons.radio_button_unchecked,
              color: controller.hasVoicePrint.value 
                  ? Colors.green 
                  : Get.theme.colorScheme.onSurface.withOpacity(0.5),
              size: 32,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    controller.hasVoicePrint.value 
                        ? 'بصمة الصوت مسجلة' 
                        : 'لم يتم تسجيل بصمة الصوت',
                    style: Get.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: controller.hasVoicePrint.value 
                          ? Colors.green 
                          : Get.theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    controller.hasVoicePrint.value 
                        ? 'يمكنك الآن استخدام الأوامر الصوتية بأمان' 
                        : 'يرجى تسجيل بصمة الصوت لضمان الأمان',
                    style: Get.textTheme.bodySmall?.copyWith(
                      color: Get.theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ));
  }

  Widget _buildRecordingArea() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text(
              'تسجيل بصمة الصوت',
              style: Get.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // أنيميشن الصوت
            Obx(() => VoiceAnimation(
              isListening: controller.isRecording.value,
              soundLevel: controller.soundLevel.value,
              size: 120,
              color: controller.isRecording.value 
                  ? Colors.red 
                  : Get.theme.colorScheme.primary,
            )),
            
            const SizedBox(height: 16),
            
            // مؤقت التسجيل
            Obx(() => controller.isRecording.value
                ? Text(
                    'مدة التسجيل: ${controller.recordingDuration.value} ثانية',
                    style: Get.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  )
                : const SizedBox.shrink()),
            
            const SizedBox(height: 16),
            
            // رسالة الحالة
            Obx(() => Text(
              controller.statusMessage.value,
              style: Get.textTheme.bodyMedium,
              textAlign: TextAlign.center,
            )),
            
            const SizedBox(height: 16),
            
            // تعليمات التسجيل
            if (!controller.isRecording.value && !controller.hasVoicePrint.value)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Get.theme.colorScheme.primaryContainer.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    Text(
                      'تعليمات التسجيل:',
                      style: Get.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '• اضغط على زر التسجيل\n'
                      '• قل جملة واضحة لمدة 5 ثوان\n'
                      '• تأكد من الهدوء حولك\n'
                      '• تحدث بصوتك الطبيعي',
                      style: Get.textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedSettings() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الإعدادات المتقدمة',
              style: Get.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // عتبة الثقة
            Obx(() => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'عتبة الثقة: ${(controller.voiceThreshold.value * 100).toInt()}%',
                  style: Get.textTheme.bodyMedium,
                ),
                Slider(
                  value: controller.voiceThreshold.value,
                  min: 0.5,
                  max: 1.0,
                  divisions: 10,
                  onChanged: (value) {
                    controller.updateVoiceSettings(threshold: value);
                  },
                ),
                Text(
                  'كلما زادت النسبة، زاد مستوى الأمان',
                  style: Get.textTheme.bodySmall?.copyWith(
                    color: Get.theme.colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            )),
            
            const SizedBox(height: 16),
            
            // لغة التعرف على الصوت
            Obx(() => DropdownButtonFormField<String>(
              value: controller.speechLanguage.value,
              decoration: const InputDecoration(
                labelText: 'لغة التعرف على الصوت',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: 'ar-SA', child: Text('العربية')),
                DropdownMenuItem(value: 'en-US', child: Text('English')),
              ],
              onChanged: (value) {
                if (value != null) {
                  controller.updateVoiceSettings(speechLang: value);
                }
              },
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Obx(() => Column(
      children: [
        // زر التسجيل/الإيقاف
        if (!controller.hasVoicePrint.value || controller.isRecording.value)
          CustomButton(
            text: controller.isRecording.value ? 'إيقاف التسجيل' : 'بدء التسجيل',
            icon: controller.isRecording.value ? Icons.stop : Icons.mic,
            backgroundColor: controller.isRecording.value ? Colors.red : null,
            onPressed: controller.isRecording.value 
                ? controller.stopRecording 
                : controller.startRecording,
            width: double.infinity,
            isLoading: controller.isProcessing.value,
          ),
        
        if (controller.hasVoicePrint.value && !controller.isRecording.value) ...[
          // زر إعادة التسجيل
          CustomButton(
            text: 'إعادة تسجيل بصمة الصوت',
            icon: Icons.refresh,
            onPressed: () => _showReRecordDialog(),
            width: double.infinity,
            isOutlined: true,
          ),
          
          const SizedBox(height: 12),
          
          // زر حذف بصمة الصوت
          CustomButton(
            text: 'حذف بصمة الصوت',
            icon: Icons.delete,
            backgroundColor: Colors.red,
            onPressed: () => _showDeleteDialog(),
            width: double.infinity,
          ),
        ],
        
        const SizedBox(height: 12),
        
        // زر الاختبار
        if (controller.hasVoicePrint.value)
          CustomButton(
            text: 'اختبار بصمة الصوت',
            icon: Icons.play_arrow,
            onPressed: () => _testVoicePrint(),
            width: double.infinity,
            isOutlined: true,
          ),
      ],
    ));
  }

  void _showReRecordDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('إعادة تسجيل بصمة الصوت'),
        content: const Text(
          'هل أنت متأكد من رغبتك في إعادة تسجيل بصمة الصوت؟ سيتم حذف البصمة الحالية.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              controller.deleteVoicePrint().then((_) {
                controller.registerVoicePrint();
              });
            },
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('حذف بصمة الصوت'),
        content: const Text(
          'هل أنت متأكد من رغبتك في حذف بصمة الصوت؟ لن تتمكن من استخدام الأوامر الصوتية الآمنة.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              controller.deleteVoicePrint();
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _testVoicePrint() {
    Get.dialog(
      AlertDialog(
        title: const Text('اختبار بصمة الصوت'),
        content: const Text(
          'سيتم تسجيل صوتك لمدة 3 ثوان ومقارنته مع البصمة المحفوظة.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Get.back();
              
              // بدء التسجيل للاختبار
              await controller.startRecording();
              await Future.delayed(const Duration(seconds: 3));
              final recordingPath = await controller.stopRecording();
              
              if (recordingPath != null) {
                final isAuthenticated = await controller.authenticateVoice(recordingPath);
                
                Get.snackbar(
                  isAuthenticated ? 'نجح الاختبار' : 'فشل الاختبار',
                  isAuthenticated 
                      ? 'تم التعرف على صوتك بنجاح' 
                      : 'لم يتم التعرف على صوتك',
                  backgroundColor: isAuthenticated ? Colors.green : Colors.red,
                  colorText: Colors.white,
                );
              }
            },
            child: const Text('بدء الاختبار'),
          ),
        ],
      ),
    );
  }
}
