import 'dart:async';
import 'package:get/get.dart';
import '../models/models.dart';
import '../services/services.dart';
import '../utils/utils.dart';

class VoiceController extends GetxController {
  // الخدمات
  final SpeechService speechService = SpeechService();
  final VoiceAuthenticationService _voiceAuthService =
      VoiceAuthenticationService();
  final DatabaseService _databaseService = DatabaseService();

  // Logger
  final Logger _logger = Logger();

  // المتغيرات التفاعلية
  final RxBool isRecording = false.obs;
  final RxBool isListening = false.obs;
  final RxBool isProcessing = false.obs;
  final RxString currentText = ''.obs;
  final RxString statusMessage = ''.obs;
  final RxDouble soundLevel = 0.0.obs;
  final RxDouble confidence = 0.0.obs;
  final RxBool hasVoicePrint = false.obs;
  final RxString recordingPath = ''.obs;
  final RxInt recordingDuration = 0.obs;

  // إعدادات الصوت
  final RxString speechLanguage = 'ar-SA'.obs;
  final RxString ttsLanguage = 'ar-SA'.obs;
  final RxDouble speechRate = 0.5.obs;
  final RxDouble volume = 1.0.obs;
  final RxDouble pitch = 1.0.obs;
  final RxDouble voiceThreshold = 0.8.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeVoiceService();
  }

  @override
  void onReady() {
    super.onReady();
    _setupListeners();
  }

  // تهيئة خدمة الصوت
  Future<void> _initializeVoiceService() async {
    try {
      await speechService.initialize();
      await _loadVoiceSettings();
      await _checkVoicePrint();
    } catch (e) {
      statusMessage.value = 'خطأ في تهيئة خدمة الصوت: $e';
      _logger.e('Error initializing voice service: $e');
    }
  }

  // إعداد المستمعين
  void _setupListeners() {
    // الاستماع لحالة التسجيل
    speechService.listeningStream.listen((listening) {
      isListening.value = listening;
      if (listening) {
        statusMessage.value = 'جاري الاستماع...';
      } else {
        statusMessage.value = 'تم إيقاف الاستماع';
      }
    });

    // الاستماع لنتائج الكلام
    speechService.speechResultStream.listen((result) {
      currentText.value = result;
    });

    // الاستماع لمستوى الصوت
    speechService.soundLevelStream.listen((level) {
      soundLevel.value = level;
    });
  }

  // تحميل إعدادات الصوت
  Future<void> _loadVoiceSettings() async {
    try {
      final user = await _databaseService.getFirstUser();
      if (user != null) {
        // تحميل الإعدادات من قاعدة البيانات
        final speechLangSetting =
            await _databaseService.getSetting(user.id!, 'speech_language');
        if (speechLangSetting != null) {
          speechLanguage.value = speechLangSetting.settingValue;
        }

        final ttsLangSetting =
            await _databaseService.getSetting(user.id!, 'tts_language');
        if (ttsLangSetting != null) {
          ttsLanguage.value = ttsLangSetting.settingValue;
        }

        final thresholdSetting =
            await _databaseService.getSetting(user.id!, 'voice_threshold');
        if (thresholdSetting != null) {
          voiceThreshold.value = double.parse(thresholdSetting.settingValue);
        }
      }
    } catch (e) {
      _logger.e('Error loading voice settings: $e');
    }
  }

  // التحقق من وجود بصمة صوتية
  Future<void> _checkVoicePrint() async {
    try {
      final user = await _databaseService.getFirstUser();
      if (user != null) {
        hasVoicePrint.value = await _voiceAuthService.hasVoicePrint(user.id!);
      }
    } catch (e) {
      _logger.e('Error checking voice print: $e');
    }
  }

  // بدء تسجيل الصوت
  Future<void> startRecording() async {
    try {
      if (isRecording.value) return;

      statusMessage.value = 'جاري بدء التسجيل...';
      final path = await speechService.startRecording();

      if (path != null) {
        isRecording.value = true;
        recordingPath.value = path;
        statusMessage.value = 'جاري التسجيل...';
        _startRecordingTimer();
      } else {
        statusMessage.value = 'فشل في بدء التسجيل';
      }
    } catch (e) {
      statusMessage.value = 'خطأ في بدء التسجيل: $e';
      _logger.e('Error starting recording: $e');
    }
  }

  // إيقاف تسجيل الصوت
  Future<String?> stopRecording() async {
    try {
      if (!isRecording.value) return null;

      statusMessage.value = 'جاري إيقاف التسجيل...';
      final path = await speechService.stopRecording();

      isRecording.value = false;
      recordingDuration.value = 0;

      if (path != null) {
        recordingPath.value = path;
        statusMessage.value = 'تم حفظ التسجيل';
        return path;
      } else {
        statusMessage.value = 'فشل في حفظ التسجيل';
        return null;
      }
    } catch (e) {
      statusMessage.value = 'خطأ في إيقاف التسجيل: $e';
      _logger.e('Error stopping recording: $e');
      return null;
    }
  }

  // إلغاء تسجيل الصوت
  Future<void> cancelRecording() async {
    try {
      await speechService.cancelRecording();
      isRecording.value = false;
      recordingDuration.value = 0;
      recordingPath.value = '';
      statusMessage.value = 'تم إلغاء التسجيل';
    } catch (e) {
      statusMessage.value = 'خطأ في إلغاء التسجيل: $e';
      _logger.e('Error cancelling recording: $e');
    }
  }

  // بدء الاستماع للكلام
  Future<void> startListening({Duration? timeout}) async {
    try {
      if (isListening.value) return;

      statusMessage.value = 'جاري بدء الاستماع...';
      await speechService.startListening(
        localeId: speechLanguage.value,
        timeout: timeout,
      );
    } catch (e) {
      statusMessage.value = 'خطأ في بدء الاستماع: $e';
      _logger.e('Error starting listening: $e');
    }
  }

  // إيقاف الاستماع
  Future<void> stopListening() async {
    try {
      await speechService.stopListening();
    } catch (e) {
      statusMessage.value = 'خطأ في إيقاف الاستماع: $e';
      _logger.e('Error stopping listening: $e');
    }
  }

  // إلغاء الاستماع
  Future<void> cancelListening() async {
    try {
      await speechService.cancelListening();
      currentText.value = '';
    } catch (e) {
      statusMessage.value = 'خطأ في إلغاء الاستماع: $e';
      _logger.e('Error cancelling listening: $e');
    }
  }

  // تحويل النص إلى كلام
  Future<void> speak(String text) async {
    try {
      statusMessage.value = 'جاري التحدث...';
      await speechService.speak(text, language: ttsLanguage.value);
      statusMessage.value = 'تم الانتهاء من التحدث';
    } catch (e) {
      statusMessage.value = 'خطأ في التحدث: $e';
      _logger.e('Error speaking: $e');
    }
  }

  // إيقاف التحدث
  Future<void> stopSpeaking() async {
    try {
      await speechService.stopSpeaking();
      statusMessage.value = 'تم إيقاف التحدث';
    } catch (e) {
      statusMessage.value = 'خطأ في إيقاف التحدث: $e';
      _logger.e('Error stopping speaking: $e');
    }
  }

  // تسجيل بصمة صوتية
  Future<bool> registerVoicePrint() async {
    try {
      statusMessage.value = 'جاري تسجيل بصمة الصوت...';
      isProcessing.value = true;

      // بدء التسجيل
      await startRecording();

      // انتظار لمدة 5 ثوان
      await Future.delayed(const Duration(seconds: 5));

      // إيقاف التسجيل
      final recordingPath = await stopRecording();

      if (recordingPath != null) {
        final user = await _databaseService.getFirstUser();
        if (user != null) {
          final success = await _voiceAuthService.registerVoicePrint(
              user.id!, recordingPath);
          if (success) {
            hasVoicePrint.value = true;
            statusMessage.value = 'تم تسجيل بصمة الصوت بنجاح';
            return true;
          } else {
            statusMessage.value = 'فشل في تسجيل بصمة الصوت';
            return false;
          }
        }
      }

      statusMessage.value = 'فشل في تسجيل بصمة الصوت';
      return false;
    } catch (e) {
      statusMessage.value = 'خطأ في تسجيل بصمة الصوت: $e';
      print('Error registering voice print: $e');
      return false;
    } finally {
      isProcessing.value = false;
    }
  }

  // التحقق من بصمة الصوت
  Future<bool> authenticateVoice(String audioPath) async {
    try {
      statusMessage.value = 'جاري التحقق من بصمة الصوت...';
      isProcessing.value = true;

      final user = await _databaseService.getFirstUser();
      if (user != null) {
        final isAuthenticated =
            await _voiceAuthService.authenticateVoice(user.id!, audioPath);

        if (isAuthenticated) {
          statusMessage.value = 'تم التحقق من بصمة الصوت بنجاح';
          return true;
        } else {
          statusMessage.value = 'فشل في التحقق من بصمة الصوت';
          return false;
        }
      }

      statusMessage.value = 'لم يتم العثور على مستخدم';
      return false;
    } catch (e) {
      statusMessage.value = 'خطأ في التحقق من بصمة الصوت: $e';
      print('Error authenticating voice: $e');
      return false;
    } finally {
      isProcessing.value = false;
    }
  }

  // حذف بصمة الصوت
  Future<bool> deleteVoicePrint() async {
    try {
      statusMessage.value = 'جاري حذف بصمة الصوت...';

      final user = await _databaseService.getFirstUser();
      if (user != null) {
        final success = await _voiceAuthService.deleteVoicePrint(user.id!);
        if (success) {
          hasVoicePrint.value = false;
          statusMessage.value = 'تم حذف بصمة الصوت';
          return true;
        }
      }

      statusMessage.value = 'فشل في حذف بصمة الصوت';
      return false;
    } catch (e) {
      statusMessage.value = 'خطأ في حذف بصمة الصوت: $e';
      print('Error deleting voice print: $e');
      return false;
    }
  }

  // تحديث إعدادات الصوت
  Future<void> updateVoiceSettings({
    String? speechLang,
    String? ttsLang,
    double? rate,
    double? vol,
    double? pitchValue,
    double? threshold,
  }) async {
    try {
      if (speechLang != null) {
        speechLanguage.value = speechLang;
        await speechService.setSpeechLanguage(speechLang);
      }

      if (ttsLang != null) {
        ttsLanguage.value = ttsLang;
        await speechService.setTtsLanguage(ttsLang);
      }

      if (rate != null) {
        speechRate.value = rate;
        await speechService.setSpeechRate(rate);
      }

      if (vol != null) {
        volume.value = vol;
        await speechService.setVolume(vol);
      }

      if (pitchValue != null) {
        pitch.value = pitchValue;
        await speechService.setPitch(pitchValue);
      }

      if (threshold != null) {
        voiceThreshold.value = threshold;
        final user = await _databaseService.getFirstUser();
        if (user != null) {
          await _voiceAuthService.updateConfidenceThreshold(
              user.id!, threshold);
        }
      }

      await _saveVoiceSettings();
      statusMessage.value = 'تم تحديث إعدادات الصوت';
    } catch (e) {
      statusMessage.value = 'خطأ في تحديث إعدادات الصوت: $e';
      print('Error updating voice settings: $e');
    }
  }

  // حفظ إعدادات الصوت
  Future<void> _saveVoiceSettings() async {
    try {
      final user = await _databaseService.getFirstUser();
      if (user != null) {
        final settings = [
          SettingModel(
            userId: user.id!,
            settingKey: 'speech_language',
            settingValue: speechLanguage.value,
            settingType: 'string',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          SettingModel(
            userId: user.id!,
            settingKey: 'tts_language',
            settingValue: ttsLanguage.value,
            settingType: 'string',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          SettingModel(
            userId: user.id!,
            settingKey: 'voice_threshold',
            settingValue: voiceThreshold.value.toString(),
            settingType: 'double',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        for (final setting in settings) {
          await _databaseService.upsertSetting(setting);
        }
      }
    } catch (e) {
      print('Error saving voice settings: $e');
    }
  }

  // مؤقت التسجيل
  void _startRecordingTimer() {
    recordingDuration.value = 0;
    Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!isRecording.value) {
        timer.cancel();
        return;
      }
      recordingDuration.value++;
    });
  }

  // الحصول على اللغات المتاحة
  Future<List<String>> getAvailableLanguages() async {
    try {
      final locales = await speechService.getAvailableLocales();
      return locales.map((locale) => locale.localeId).toList();
    } catch (e) {
      _logger.e('Error getting available languages: $e');
      return ['ar-SA', 'en-US'];
    }
  }

  @override
  void onClose() {
    speechService.dispose();
    super.onClose();
  }
}
