import 'dart:io';
import 'dart:math';
import 'dart:typed_data';
import 'package:flutter/foundation.dart' show kIsWeb;

import '../models/models.dart';
import '../services/database_service.dart';
import '../utils/utils.dart';

class VoiceAuthenticationService {
  static final VoiceAuthenticationService _instance =
      VoiceAuthenticationService._internal();
  factory VoiceAuthenticationService() => _instance;
  VoiceAuthenticationService._internal();

  final DatabaseService _databaseService = DatabaseService();

  // Logger
  final Logger _logger = Logger();

  // تسجيل بصمة صوتية جديدة
  Future<bool> registerVoicePrint(int userId, String audioFilePath) async {
    try {
      if (kIsWeb) {
        // في بيئة الويب، نستخدم بيانات وهمية للاختبار
        _logger.w('Web environment detected. Using mock data for voice print.');
        final mockAudioData = Uint8List.fromList(
            List.generate(1000, (i) => Random().nextInt(256)));
        return await _processVoicePrintData(userId, mockAudioData);
      }

      // قراءة ملف الصوت
      final audioFile = File(audioFilePath);
      if (!await audioFile.exists()) {
        throw Exception('Audio file not found');
      }

      final audioData = await audioFile.readAsBytes();

      return await _processVoicePrintData(userId, audioData);
    } catch (e) {
      _logger.e('Error registering voice print: $e');
      return false;
    }
  }

  // معالجة بيانات بصمة الصوت
  Future<bool> _processVoicePrintData(int userId, Uint8List audioData) async {
    try {
      // استخراج الميزات الصوتية
      final voiceFeatures = await _extractVoiceFeatures(audioData);

      // إنشاء نموذج بصمة الصوت
      final voicePrint = VoicePrintModel(
        userId: userId,
        voiceData: audioData,
        voiceFeatures: voiceFeatures.toJson().toString(),
        confidenceThreshold: 0.8,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // حفظ في قاعدة البيانات
      await _databaseService.insertVoicePrint(voicePrint);

      return true;
    } catch (e) {
      _logger.e('Error processing voice print data: $e');
      return false;
    }
  }

  // التحقق من بصمة الصوت
  Future<bool> authenticateVoice(int userId, String audioFilePath) async {
    try {
      if (kIsWeb) {
        // في بيئة الويب، نستخدم بيانات وهمية للاختبار
        _logger.w(
            'Web environment detected. Using mock data for voice verification.');
        return true; // نفترض أن التحقق ناجح دائمًا في بيئة الويب
      }

      // الحصول على بصمة الصوت المسجلة
      final storedVoicePrint =
          await _databaseService.getVoicePrintByUserId(userId);
      if (storedVoicePrint == null) {
        _logger.w('No voice print found for user');
        return false;
      }

      // قراءة ملف الصوت الجديد
      final audioFile = File(audioFilePath);
      if (!await audioFile.exists()) {
        throw Exception('Audio file not found');
      }

      final newAudioData = await audioFile.readAsBytes();

      // استخراج الميزات الصوتية للصوت الجديد
      final newVoiceFeatures = await _extractVoiceFeatures(newAudioData);

      // تحليل الميزات المحفوظة
      final storedFeatures = VoiceFeatures.fromJson(Map<String, dynamic>.from(
          // هنا سيتم تحليل JSON من النص المحفوظ
          {}));

      // مقارنة الميزات
      final similarity = _calculateSimilarity(newVoiceFeatures, storedFeatures);

      // التحقق من تجاوز العتبة
      return similarity >= storedVoicePrint.confidenceThreshold;
    } catch (e) {
      _logger.e('Error authenticating voice: $e');
      return false;
    }
  }

  // استخراج الميزات الصوتية (تطبيق مبسط)
  Future<VoiceFeatures> _extractVoiceFeatures(Uint8List audioData) async {
    // هذا تطبيق مبسط لاستخراج الميزات الصوتية
    // في التطبيق الحقيقي، ستحتاج لمكتبة متخصصة لمعالجة الإشارات الصوتية

    // حساب الطاقة
    double energy = 0.0;
    for (int i = 0; i < audioData.length; i += 2) {
      if (i + 1 < audioData.length) {
        int sample = (audioData[i + 1] << 8) | audioData[i];
        energy += sample * sample;
      }
    }
    energy = sqrt(energy / (audioData.length / 2));

    // حساب التردد الأساسي (مبسط)
    double pitch = _calculatePitch(audioData);

    // حساب المركز الطيفي (مبسط)
    double spectralCentroid = _calculateSpectralCentroid(audioData);

    // حساب MFCC (مبسط)
    List<double> mfcc = _calculateMFCC(audioData);

    // حساب الفورمانت (مبسط)
    List<double> formants = _calculateFormants(audioData);

    // مدة التسجيل (تقديرية)
    double duration = audioData.length / (44100 * 2); // افتراض 44.1kHz, 16-bit

    return VoiceFeatures(
      mfcc: mfcc,
      pitch: pitch,
      energy: energy,
      spectralCentroid: spectralCentroid,
      formants: formants,
      duration: duration,
    );
  }

  // حساب التردد الأساسي (مبسط)
  double _calculatePitch(Uint8List audioData) {
    // تطبيق مبسط لحساب التردد الأساسي
    // في التطبيق الحقيقي، ستستخدم خوارزميات أكثر تعقيداً مثل YIN أو RAPT

    List<double> samples = [];
    for (int i = 0; i < audioData.length; i += 2) {
      if (i + 1 < audioData.length) {
        int sample = (audioData[i + 1] << 8) | audioData[i];
        samples.add(sample.toDouble());
      }
    }

    // حساب الارتباط الذاتي
    double maxCorrelation = 0.0;
    int bestLag = 0;

    for (int lag = 50; lag < 400; lag++) {
      // نطاق التردد المتوقع للصوت البشري
      double correlation = 0.0;
      int count = 0;

      for (int i = 0; i < samples.length - lag; i++) {
        correlation += samples[i] * samples[i + lag];
        count++;
      }

      if (count > 0) {
        correlation /= count;
        if (correlation > maxCorrelation) {
          maxCorrelation = correlation;
          bestLag = lag;
        }
      }
    }

    // تحويل إلى تردد
    double sampleRate = 44100.0; // افتراض معدل العينة
    return bestLag > 0 ? sampleRate / bestLag : 0.0;
  }

  // حساب المركز الطيفي (مبسط)
  double _calculateSpectralCentroid(Uint8List audioData) {
    // تطبيق مبسط للمركز الطيفي
    double weightedSum = 0.0;
    double magnitudeSum = 0.0;

    for (int i = 0; i < audioData.length; i += 2) {
      if (i + 1 < audioData.length) {
        int sample = (audioData[i + 1] << 8) | audioData[i];
        double magnitude = sample.abs().toDouble();
        weightedSum += i * magnitude;
        magnitudeSum += magnitude;
      }
    }

    return magnitudeSum > 0 ? weightedSum / magnitudeSum : 0.0;
  }

  // حساب MFCC (مبسط)
  List<double> _calculateMFCC(Uint8List audioData) {
    // تطبيق مبسط جداً لـ MFCC
    // في التطبيق الحقيقي، ستحتاج لتطبيق FFT وفلاتر Mel

    List<double> mfcc = [];
    int numCoefficients = 13;

    for (int i = 0; i < numCoefficients; i++) {
      double coefficient = 0.0;
      int step = audioData.length ~/ numCoefficients;

      for (int j = i * step;
          j < (i + 1) * step && j + 1 < audioData.length;
          j += 2) {
        int sample = (audioData[j + 1] << 8) | audioData[j];
        coefficient += sample.abs();
      }

      mfcc.add(coefficient / step);
    }

    return mfcc;
  }

  // حساب الفورمانت (مبسط)
  List<double> _calculateFormants(Uint8List audioData) {
    // تطبيق مبسط للفورمانت
    // في التطبيق الحقيقي، ستحتاج لتحليل LPC أو تحليل طيفي متقدم

    return [800.0, 1200.0, 2400.0]; // قيم افتراضية للفورمانت
  }

  // حساب التشابه بين بصمتين صوتيتين
  double _calculateSimilarity(
      VoiceFeatures features1, VoiceFeatures features2) {
    double totalSimilarity = 0.0;
    int comparisons = 0;

    // مقارنة التردد الأساسي
    double pitchSimilarity = 1.0 -
        (features1.pitch - features2.pitch).abs() /
            max(features1.pitch, features2.pitch);
    totalSimilarity += pitchSimilarity * 0.3; // وزن 30%
    comparisons++;

    // مقارنة الطاقة
    double energySimilarity = 1.0 -
        (features1.energy - features2.energy).abs() /
            max(features1.energy, features2.energy);
    totalSimilarity += energySimilarity * 0.2; // وزن 20%
    comparisons++;

    // مقارنة المركز الطيفي
    double spectralSimilarity = 1.0 -
        (features1.spectralCentroid - features2.spectralCentroid).abs() /
            max(features1.spectralCentroid, features2.spectralCentroid);
    totalSimilarity += spectralSimilarity * 0.2; // وزن 20%
    comparisons++;

    // مقارنة MFCC
    double mfccSimilarity =
        _calculateVectorSimilarity(features1.mfcc, features2.mfcc);
    totalSimilarity += mfccSimilarity * 0.3; // وزن 30%
    comparisons++;

    return comparisons > 0 ? totalSimilarity / comparisons : 0.0;
  }

  // حساب التشابه بين متجهين
  double _calculateVectorSimilarity(
      List<double> vector1, List<double> vector2) {
    if (vector1.length != vector2.length) return 0.0;

    double dotProduct = 0.0;
    double norm1 = 0.0;
    double norm2 = 0.0;

    for (int i = 0; i < vector1.length; i++) {
      dotProduct += vector1[i] * vector2[i];
      norm1 += vector1[i] * vector1[i];
      norm2 += vector2[i] * vector2[i];
    }

    norm1 = sqrt(norm1);
    norm2 = sqrt(norm2);

    if (norm1 == 0.0 || norm2 == 0.0) return 0.0;

    return dotProduct / (norm1 * norm2);
  }

  // تحديث عتبة الثقة
  Future<bool> updateConfidenceThreshold(int userId, double threshold) async {
    try {
      final voicePrint = await _databaseService.getVoicePrintByUserId(userId);
      if (voicePrint == null) return false;

      final updatedVoicePrint = voicePrint.copyWith(
        confidenceThreshold: threshold,
        updatedAt: DateTime.now(),
      );

      await _databaseService.updateVoicePrint(updatedVoicePrint);
      return true;
    } catch (e) {
      _logger.e('Error updating confidence threshold: $e');
      return false;
    }
  }

  // حذف بصمة الصوت
  Future<bool> deleteVoicePrint(int userId) async {
    try {
      final voicePrint = await _databaseService.getVoicePrintByUserId(userId);
      if (voicePrint == null) return false;

      await _databaseService.deleteVoicePrint(voicePrint.id!);
      return true;
    } catch (e) {
      _logger.e('Error deleting voice print: $e');
      return false;
    }
  }

  // التحقق من وجود بصمة صوتية
  Future<bool> hasVoicePrint(int userId) async {
    if (kIsWeb) {
      // في بيئة الويب، نفترض أنه لا توجد بصمة صوتية
      _logger.w('Web environment detected. Assuming no voice print exists.');
      return false;
    }

    final voicePrint = await _databaseService.getVoicePrintByUserId(userId);
    return voicePrint != null;
  }
}
