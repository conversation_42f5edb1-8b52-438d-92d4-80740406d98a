import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/controllers.dart';
import '../widgets/widgets.dart';

class HomeView extends GetView<HomeController> {
  const HomeView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      appBar: AppBar(
        title: Text(
          'Smart Talk',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onPrimary,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(
              Icons.settings,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            onPressed: () => Get.toNamed('/settings'),
          ),
        ],
      ),
      body: Obx(() {
        if (!controller.isInitialized.value) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // بطاقة الترحيب
              _buildWelcomeCard(),
              
              const SizedBox(height: 20),
              
              // زر الميكروفون الرئيسي
              _buildMicrophoneButton(),
              
              const SizedBox(height: 20),
              
              // عرض حالة التطبيق
              _buildStatusCard(),
              
              const SizedBox(height: 20),
              
              // مستوى الصوت
              _buildSoundLevelIndicator(),
              
              const SizedBox(height: 20),
              
              // الأوامر السريعة
              _buildQuickCommands(),
              
              const SizedBox(height: 20),
              
              // الإحصائيات
              _buildStatistics(),
            ],
          ),
        );
      }),
      bottomNavigationBar: _buildBottomNavigation(),
    );
  }

  Widget _buildWelcomeCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Icon(
              Icons.waving_hand,
              size: 48,
              color: Get.theme.colorScheme.primary,
            ),
            const SizedBox(height: 8),
            Obx(() => Text(
              controller.currentUser.value != null
                  ? 'مرحباً ${controller.currentUser.value!.name}'
                  : 'مرحباً بك في Smart Talk',
              style: Get.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            )),
            const SizedBox(height: 8),
            Text(
              'اضغط على الميكروفون وقل أمرك الصوتي',
              style: Get.textTheme.bodyMedium?.copyWith(
                color: Get.theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMicrophoneButton() {
    return Obx(() => GestureDetector(
      onTap: controller.isListening.value 
          ? controller.stopListening 
          : controller.startListening,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: 120,
        height: 120,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: LinearGradient(
            colors: controller.isListening.value
                ? [Colors.red.shade400, Colors.red.shade600]
                : [Get.theme.colorScheme.primary, Get.theme.colorScheme.primaryContainer],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
              color: controller.isListening.value
                  ? Colors.red.withOpacity(0.3)
                  : Get.theme.colorScheme.primary.withOpacity(0.3),
              blurRadius: 20,
              spreadRadius: controller.isListening.value ? 8 : 4,
            ),
          ],
        ),
        child: Icon(
          controller.isListening.value ? Icons.mic : Icons.mic_none,
          size: 48,
          color: Colors.white,
        ),
      ),
    ));
  }

  Widget _buildStatusCard() {
    return Obx(() => Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  controller.isListening.value 
                      ? Icons.hearing 
                      : Icons.hearing_disabled,
                  color: controller.isListening.value 
                      ? Colors.green 
                      : Get.theme.colorScheme.onSurface.withOpacity(0.5),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    controller.statusMessage.value,
                    style: Get.textTheme.bodyMedium,
                  ),
                ),
              ],
            ),
            if (controller.lastCommand.value.isNotEmpty) ...[
              const SizedBox(height: 12),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Get.theme.colorScheme.primaryContainer.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'آخر أمر:',
                      style: Get.textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      controller.lastCommand.value,
                      style: Get.textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    ));
  }

  Widget _buildSoundLevelIndicator() {
    return Obx(() => controller.isListening.value
        ? Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Text(
                    'مستوى الصوت',
                    style: Get.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  LinearProgressIndicator(
                    value: controller.soundLevel.value.clamp(0.0, 1.0),
                    backgroundColor: Get.theme.colorScheme.surfaceVariant,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Get.theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
          )
        : const SizedBox.shrink());
  }

  Widget _buildQuickCommands() {
    final quickCommands = [
      {
        'title': 'فتح YouTube',
        'icon': Icons.play_circle_outline,
        'command': 'افتح يوتيوب',
      },
      {
        'title': 'جهات الاتصال',
        'icon': Icons.contacts,
        'command': 'افتح جهات الاتصال',
      },
      {
        'title': 'إجراء مكالمة',
        'icon': Icons.phone,
        'command': 'اتصل بـ...',
      },
      {
        'title': 'إرسال رسالة',
        'icon': Icons.message,
        'command': 'أرسل رسالة إلى...',
      },
    ];

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'أوامر سريعة',
              style: Get.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 2.5,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: quickCommands.length,
              itemBuilder: (context, index) {
                final command = quickCommands[index];
                return InkWell(
                  onTap: () {
                    // تنفيذ الأمر السريع
                    Get.snackbar(
                      'أمر سريع',
                      'قل: "${command['command']}"',
                      snackPosition: SnackPosition.BOTTOM,
                    );
                  },
                  borderRadius: BorderRadius.circular(8),
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Get.theme.colorScheme.outline.withOpacity(0.3),
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          command['icon'] as IconData,
                          size: 20,
                          color: Get.theme.colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            command['title'] as String,
                            style: Get.textTheme.bodySmall,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatistics() {
    return Obx(() => Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الإحصائيات',
              style: Get.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'جهات الاتصال',
                    controller.statistics['contacts']?.toString() ?? '0',
                    Icons.contacts,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'الأوامر',
                    controller.statistics['commands']?.toString() ?? '0',
                    Icons.mic,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'نجح',
                    controller.statistics['successful_commands']?.toString() ?? '0',
                    Icons.check_circle,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'الأنشطة',
                    controller.statistics['activities']?.toString() ?? '0',
                    Icons.history,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    ));
  }

  Widget _buildStatItem(String title, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(8),
      child: Column(
        children: [
          Icon(
            icon,
            size: 24,
            color: Get.theme.colorScheme.primary,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Get.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Get.theme.colorScheme.primary,
            ),
          ),
          Text(
            title,
            style: Get.textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigation() {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      selectedItemColor: Get.theme.colorScheme.primary,
      unselectedItemColor: Get.theme.colorScheme.onSurface.withOpacity(0.6),
      currentIndex: 0,
      onTap: (index) {
        switch (index) {
          case 0:
            // الصفحة الرئيسية - نحن فيها بالفعل
            break;
          case 1:
            Get.toNamed('/contacts');
            break;
          case 2:
            Get.toNamed('/voice-setup');
            break;
          case 3:
            Get.toNamed('/settings');
            break;
        }
      },
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.home),
          label: 'الرئيسية',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.contacts),
          label: 'جهات الاتصال',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.mic_external_on),
          label: 'بصمة الصوت',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.settings),
          label: 'الإعدادات',
        ),
      ],
    );
  }
}
