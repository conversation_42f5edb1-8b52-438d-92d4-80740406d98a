import 'package:flutter/material.dart';
import 'dart:math' as math;

class VoiceAnimation extends StatefulWidget {
  final bool isListening;
  final double soundLevel;
  final double size;
  final Color? color;

  const VoiceAnimation({
    super.key,
    required this.isListening,
    this.soundLevel = 0.0,
    this.size = 100,
    this.color,
  });

  @override
  State<VoiceAnimation> createState() => _VoiceAnimationState();
}

class _VoiceAnimationState extends State<VoiceAnimation>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _waveController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _waveController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _pulseController.repeat(reverse: true);
    _waveController.repeat();
  }

  @override
  void didUpdateWidget(VoiceAnimation oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isListening != oldWidget.isListening) {
      if (widget.isListening) {
        _pulseController.repeat(reverse: true);
        _waveController.repeat();
      } else {
        _pulseController.stop();
        _waveController.stop();
      }
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _waveController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // الدوائر المتموجة
          if (widget.isListening) ...[
            AnimatedBuilder(
              animation: _waveController,
              builder: (context, child) {
                return CustomPaint(
                  size: Size(widget.size, widget.size),
                  painter: WavePainter(
                    animation: _waveController.value,
                    soundLevel: widget.soundLevel,
                    color: widget.color ?? Theme.of(context).colorScheme.primary,
                  ),
                );
              },
            ),
          ],
          
          // الدائرة الرئيسية
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: widget.isListening ? _pulseAnimation.value : 1.0,
                child: Container(
                  width: widget.size * 0.6,
                  height: widget.size * 0.6,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: widget.color ?? Theme.of(context).colorScheme.primary,
                    boxShadow: widget.isListening
                        ? [
                            BoxShadow(
                              color: (widget.color ?? Theme.of(context).colorScheme.primary)
                                  .withOpacity(0.3),
                              blurRadius: 20,
                              spreadRadius: 5,
                            ),
                          ]
                        : null,
                  ),
                  child: Icon(
                    widget.isListening ? Icons.mic : Icons.mic_none,
                    size: widget.size * 0.3,
                    color: Colors.white,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

class WavePainter extends CustomPainter {
  final double animation;
  final double soundLevel;
  final Color color;

  WavePainter({
    required this.animation,
    required this.soundLevel,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final baseRadius = size.width * 0.3;
    
    // رسم 3 دوائر متموجة
    for (int i = 0; i < 3; i++) {
      final progress = (animation + i * 0.3) % 1.0;
      final radius = baseRadius + (progress * baseRadius * 0.8);
      final opacity = (1.0 - progress) * (0.3 + soundLevel * 0.7);
      
      final paint = Paint()
        ..color = color.withOpacity(opacity)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0;
      
      canvas.drawCircle(center, radius, paint);
    }
  }

  @override
  bool shouldRepaint(WavePainter oldDelegate) {
    return oldDelegate.animation != animation ||
           oldDelegate.soundLevel != soundLevel;
  }
}

class SoundWaveWidget extends StatefulWidget {
  final bool isActive;
  final double soundLevel;
  final int barsCount;
  final double width;
  final double height;
  final Color? color;

  const SoundWaveWidget({
    super.key,
    required this.isActive,
    this.soundLevel = 0.0,
    this.barsCount = 5,
    this.width = 100,
    this.height = 40,
    this.color,
  });

  @override
  State<SoundWaveWidget> createState() => _SoundWaveWidgetState();
}

class _SoundWaveWidgetState extends State<SoundWaveWidget>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _animations = List.generate(widget.barsCount, (index) {
      return Tween<double>(
        begin: 0.1,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: _controller,
        curve: Interval(
          index * 0.1,
          1.0,
          curve: Curves.easeInOut,
        ),
      ));
    });

    if (widget.isActive) {
      _controller.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(SoundWaveWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isActive != oldWidget.isActive) {
      if (widget.isActive) {
        _controller.repeat(reverse: true);
      } else {
        _controller.stop();
        _controller.reset();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width,
      height: widget.height,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: List.generate(widget.barsCount, (index) {
              final animationValue = widget.isActive 
                  ? _animations[index].value 
                  : 0.1;
              final heightMultiplier = animationValue * (0.5 + widget.soundLevel * 0.5);
              
              return Container(
                width: widget.width / widget.barsCount * 0.6,
                height: widget.height * heightMultiplier,
                decoration: BoxDecoration(
                  color: widget.color ?? Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(2),
                ),
              );
            }),
          );
        },
      ),
    );
  }
}
