class ActivityLogModel {
  final int? id;
  final int userId;
  final String activityType;
  final String activityDescription;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;

  ActivityLogModel({
    this.id,
    required this.userId,
    required this.activityType,
    required this.activityDescription,
    this.metadata,
    required this.createdAt,
  });

  // تحويل من Map إلى ActivityLogModel
  factory ActivityLogModel.fromMap(Map<String, dynamic> map) {
    Map<String, dynamic>? metadataMap;
    if (map['metadata'] != null) {
      try {
        metadataMap = Map<String, dynamic>.from(
          map['metadata'] is String 
            ? {} // سيتم تحليل JSON هنا لاحقاً
            : map['metadata']
        );
      } catch (e) {
        metadataMap = null;
      }
    }

    return ActivityLogModel(
      id: map['id'],
      userId: map['user_id'],
      activityType: map['activity_type'],
      activityDescription: map['activity_description'],
      metadata: metadataMap,
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  // تحويل من ActivityLogModel إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'activity_type': activityType,
      'activity_description': activityDescription,
      'metadata': metadata?.toString(), // سيتم تحويل إلى JSON لاحقاً
      'created_at': createdAt.toIso8601String(),
    };
  }

  // إنشاء نسخة محدثة من ActivityLogModel
  ActivityLogModel copyWith({
    int? id,
    int? userId,
    String? activityType,
    String? activityDescription,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
  }) {
    return ActivityLogModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      activityType: activityType ?? this.activityType,
      activityDescription: activityDescription ?? this.activityDescription,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'ActivityLogModel{id: $id, activityType: $activityType, activityDescription: $activityDescription}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ActivityLogModel &&
        other.id == id &&
        other.userId == userId &&
        other.activityType == activityType &&
        other.activityDescription == activityDescription;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        userId.hashCode ^
        activityType.hashCode ^
        activityDescription.hashCode;
  }
}

// أنواع الأنشطة
enum ActivityType {
  voiceCommand,     // أمر صوتي
  phoneCall,        // مكالمة هاتفية
  smsMessage,       // رسالة نصية
  whatsappMessage,  // رسالة واتساب
  youtubeAction,    // إجراء يوتيوب
  contactAction,    // إجراء جهة اتصال
  settingChange,    // تغيير إعداد
  voiceRegistration, // تسجيل صوت
  authentication,   // مصادقة
  error,            // خطأ
  systemAction,     // إجراء نظام
}

// مستويات الأنشطة
enum ActivityLevel {
  info,     // معلومات
  warning,  // تحذير
  error,    // خطأ
  success,  // نجاح
  debug,    // تصحيح
}
