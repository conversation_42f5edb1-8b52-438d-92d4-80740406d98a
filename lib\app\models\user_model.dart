class UserModel {
  final int? id;
  final String name;
  final String? voicePrintPath;
  final String language;
  final String themeMode;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserModel({
    this.id,
    required this.name,
    this.voicePrintPath,
    this.language = 'ar',
    this.themeMode = 'light',
    required this.createdAt,
    required this.updatedAt,
  });

  // تحويل من Map إلى UserModel
  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      id: map['id'],
      name: map['name'],
      voicePrintPath: map['voice_print_path'],
      language: map['language'] ?? 'ar',
      themeMode: map['theme_mode'] ?? 'light',
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  // تحويل من UserModel إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'voice_print_path': voicePrintPath,
      'language': language,
      'theme_mode': themeMode,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // إنشاء نسخة محدثة من UserModel
  UserModel copyWith({
    int? id,
    String? name,
    String? voicePrintPath,
    String? language,
    String? themeMode,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      voicePrintPath: voicePrintPath ?? this.voicePrintPath,
      language: language ?? this.language,
      themeMode: themeMode ?? this.themeMode,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'UserModel{id: $id, name: $name, language: $language, themeMode: $themeMode}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel &&
        other.id == id &&
        other.name == name &&
        other.voicePrintPath == voicePrintPath &&
        other.language == language &&
        other.themeMode == themeMode;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        voicePrintPath.hashCode ^
        language.hashCode ^
        themeMode.hashCode;
  }
}
