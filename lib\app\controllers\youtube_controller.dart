import 'package:get/get.dart';
import '../services/services.dart';

class YouTubeController extends GetxController {
  // الخدمات
  final YouTubeService _youtubeService = YouTubeService();

  // المتغيرات التفاعلية
  final RxBool isLoading = false.obs;
  final RxString statusMessage = ''.obs;
  final RxString currentVideoId = ''.obs;
  final RxString currentVideoTitle = ''.obs;
  final RxString lastSearchQuery = ''.obs;
  final RxBool isYouTubeAppInstalled = false.obs;
  final RxBool isPlaying = false.obs;
  final RxBool isFullscreen = false.obs;

  // قوائم الفيديوهات
  final RxList<Map<String, dynamic>> searchResults = <Map<String, dynamic>>[].obs;
  final RxList<String> recentSearches = <String>[].obs;
  final RxList<String> favoriteVideos = <String>[].obs;

  @override
  void onInit() {
    super.onInit();
    _initializeYouTube();
  }

  // تهيئة YouTube
  Future<void> _initializeYouTube() async {
    try {
      isLoading.value = true;
      statusMessage.value = 'جاري فحص تطبيق YouTube...';

      // التحقق من توفر تطبيق YouTube
      isYouTubeAppInstalled.value = await _youtubeService.isYouTubeAppInstalled();
      
      if (isYouTubeAppInstalled.value) {
        statusMessage.value = 'تطبيق YouTube متوفر';
      } else {
        statusMessage.value = 'تطبيق YouTube غير مثبت - سيتم استخدام المتصفح';
      }
    } catch (e) {
      statusMessage.value = 'خطأ في تهيئة YouTube: $e';
    } finally {
      isLoading.value = false;
    }
  }

  // فتح تطبيق YouTube
  Future<void> openYouTube() async {
    try {
      isLoading.value = true;
      statusMessage.value = 'جاري فتح YouTube...';

      final success = await _youtubeService.openYouTube();
      
      if (success) {
        statusMessage.value = 'تم فتح YouTube بنجاح';
      } else {
        statusMessage.value = 'فشل في فتح YouTube';
      }
    } catch (e) {
      statusMessage.value = 'خطأ في فتح YouTube: $e';
    } finally {
      isLoading.value = false;
    }
  }

  // البحث عن فيديو وتشغيله
  Future<void> searchAndPlayVideo(String query) async {
    try {
      if (query.isEmpty) {
        statusMessage.value = 'يرجى إدخال كلمة البحث';
        return;
      }

      isLoading.value = true;
      statusMessage.value = 'جاري البحث عن الفيديو...';

      // إضافة إلى البحثات الأخيرة
      _addToRecentSearches(query);
      lastSearchQuery.value = query;

      final success = await _youtubeService.searchAndPlayVideo(query);
      
      if (success) {
        statusMessage.value = 'تم تشغيل الفيديو';
        isPlaying.value = true;
      } else {
        statusMessage.value = 'فشل في تشغيل الفيديو';
      }
    } catch (e) {
      statusMessage.value = 'خطأ في البحث عن الفيديو: $e';
    } finally {
      isLoading.value = false;
    }
  }

  // تشغيل فيديو بمعرف محدد
  Future<void> playVideoById(String videoId) async {
    try {
      isLoading.value = true;
      statusMessage.value = 'جاري تشغيل الفيديو...';

      final success = await _youtubeService.playVideoById(videoId);
      
      if (success) {
        currentVideoId.value = videoId;
        statusMessage.value = 'تم تشغيل الفيديو';
        isPlaying.value = true;
      } else {
        statusMessage.value = 'فشل في تشغيل الفيديو';
      }
    } catch (e) {
      statusMessage.value = 'خطأ في تشغيل الفيديو: $e';
    } finally {
      isLoading.value = false;
    }
  }

  // إيقاف مؤقت للفيديو
  Future<void> pauseVideo() async {
    try {
      statusMessage.value = 'جاري إيقاف الفيديو...';

      final success = await _youtubeService.pauseVideo();
      
      if (success) {
        statusMessage.value = 'تم إيقاف الفيديو مؤقتاً';
        isPlaying.value = false;
      } else {
        statusMessage.value = 'فشل في إيقاف الفيديو';
      }
    } catch (e) {
      statusMessage.value = 'خطأ في إيقاف الفيديو: $e';
    }
  }

  // استئناف تشغيل الفيديو
  Future<void> resumeVideo() async {
    try {
      statusMessage.value = 'جاري استئناف التشغيل...';

      final success = await _youtubeService.resumeVideo();
      
      if (success) {
        statusMessage.value = 'تم استئناف التشغيل';
        isPlaying.value = true;
      } else {
        statusMessage.value = 'فشل في استئناف التشغيل';
      }
    } catch (e) {
      statusMessage.value = 'خطأ في استئناف التشغيل: $e';
    }
  }

  // الانتقال للفيديو التالي
  Future<void> nextVideo() async {
    try {
      statusMessage.value = 'جاري الانتقال للفيديو التالي...';

      final success = await _youtubeService.nextVideo();
      
      if (success) {
        statusMessage.value = 'تم الانتقال للفيديو التالي';
      } else {
        statusMessage.value = 'فشل في الانتقال للفيديو التالي';
      }
    } catch (e) {
      statusMessage.value = 'خطأ في الانتقال للفيديو التالي: $e';
    }
  }

  // الانتقال للفيديو السابق
  Future<void> previousVideo() async {
    try {
      statusMessage.value = 'جاري الانتقال للفيديو السابق...';

      final success = await _youtubeService.previousVideo();
      
      if (success) {
        statusMessage.value = 'تم الانتقال للفيديو السابق';
      } else {
        statusMessage.value = 'فشل في الانتقال للفيديو السابق';
      }
    } catch (e) {
      statusMessage.value = 'خطأ في الانتقال للفيديو السابق: $e';
    }
  }

  // تبديل وضع ملء الشاشة
  Future<void> toggleFullscreen() async {
    try {
      statusMessage.value = 'جاري تبديل وضع ملء الشاشة...';

      final success = await _youtubeService.toggleFullscreen();
      
      if (success) {
        isFullscreen.value = !isFullscreen.value;
        statusMessage.value = isFullscreen.value 
            ? 'تم تفعيل وضع ملء الشاشة' 
            : 'تم إلغاء وضع ملء الشاشة';
      } else {
        statusMessage.value = 'فشل في تبديل وضع ملء الشاشة';
      }
    } catch (e) {
      statusMessage.value = 'خطأ في تبديل وضع ملء الشاشة: $e';
    }
  }

  // فتح قناة معينة
  Future<void> openChannel(String channelName) async {
    try {
      if (channelName.isEmpty) {
        statusMessage.value = 'يرجى إدخال اسم القناة';
        return;
      }

      isLoading.value = true;
      statusMessage.value = 'جاري فتح القناة...';

      final success = await _youtubeService.openChannel(channelName);
      
      if (success) {
        statusMessage.value = 'تم فتح القناة';
      } else {
        statusMessage.value = 'فشل في فتح القناة';
      }
    } catch (e) {
      statusMessage.value = 'خطأ في فتح القناة: $e';
    } finally {
      isLoading.value = false;
    }
  }

  // فتح قائمة تشغيل
  Future<void> openPlaylist(String playlistName) async {
    try {
      if (playlistName.isEmpty) {
        statusMessage.value = 'يرجى إدخال اسم قائمة التشغيل';
        return;
      }

      isLoading.value = true;
      statusMessage.value = 'جاري فتح قائمة التشغيل...';

      final success = await _youtubeService.openPlaylist(playlistName);
      
      if (success) {
        statusMessage.value = 'تم فتح قائمة التشغيل';
      } else {
        statusMessage.value = 'فشل في فتح قائمة التشغيل';
      }
    } catch (e) {
      statusMessage.value = 'خطأ في فتح قائمة التشغيل: $e';
    } finally {
      isLoading.value = false;
    }
  }

  // فتح الفيديوهات الشائعة
  Future<void> openTrending() async {
    try {
      isLoading.value = true;
      statusMessage.value = 'جاري فتح الفيديوهات الشائعة...';

      final success = await _youtubeService.openTrending();
      
      if (success) {
        statusMessage.value = 'تم فتح الفيديوهات الشائعة';
      } else {
        statusMessage.value = 'فشل في فتح الفيديوهات الشائعة';
      }
    } catch (e) {
      statusMessage.value = 'خطأ في فتح الفيديوهات الشائعة: $e';
    } finally {
      isLoading.value = false;
    }
  }

  // فتح الاشتراكات
  Future<void> openSubscriptions() async {
    try {
      isLoading.value = true;
      statusMessage.value = 'جاري فتح الاشتراكات...';

      final success = await _youtubeService.openSubscriptions();
      
      if (success) {
        statusMessage.value = 'تم فتح الاشتراكات';
      } else {
        statusMessage.value = 'فشل في فتح الاشتراكات';
      }
    } catch (e) {
      statusMessage.value = 'خطأ في فتح الاشتراكات: $e';
    } finally {
      isLoading.value = false;
    }
  }

  // فتح المكتبة
  Future<void> openLibrary() async {
    try {
      isLoading.value = true;
      statusMessage.value = 'جاري فتح المكتبة...';

      final success = await _youtubeService.openLibrary();
      
      if (success) {
        statusMessage.value = 'تم فتح المكتبة';
      } else {
        statusMessage.value = 'فشل في فتح المكتبة';
      }
    } catch (e) {
      statusMessage.value = 'خطأ في فتح المكتبة: $e';
    } finally {
      isLoading.value = false;
    }
  }

  // البحث عن فيديوهات متعددة
  Future<void> searchVideos(String query, {int maxResults = 10}) async {
    try {
      if (query.isEmpty) {
        statusMessage.value = 'يرجى إدخال كلمة البحث';
        return;
      }

      isLoading.value = true;
      statusMessage.value = 'جاري البحث...';

      final results = await _youtubeService.searchVideos(query, maxResults: maxResults);
      searchResults.value = results;
      
      _addToRecentSearches(query);
      lastSearchQuery.value = query;

      statusMessage.value = 'تم العثور على ${results.length} نتيجة';
    } catch (e) {
      statusMessage.value = 'خطأ في البحث: $e';
    } finally {
      isLoading.value = false;
    }
  }

  // الحصول على معلومات الفيديو
  Future<Map<String, dynamic>?> getVideoInfo(String videoId) async {
    try {
      return await _youtubeService.getVideoInfo(videoId);
    } catch (e) {
      statusMessage.value = 'خطأ في الحصول على معلومات الفيديو: $e';
      return null;
    }
  }

  // إضافة إلى البحثات الأخيرة
  void _addToRecentSearches(String query) {
    if (query.isEmpty) return;
    
    // إزالة البحث إذا كان موجوداً مسبقاً
    recentSearches.remove(query);
    
    // إضافة في المقدمة
    recentSearches.insert(0, query);
    
    // الاحتفاظ بآخر 10 بحثات فقط
    if (recentSearches.length > 10) {
      recentSearches.removeRange(10, recentSearches.length);
    }
  }

  // إضافة فيديو للمفضلة
  void addToFavorites(String videoId) {
    if (!favoriteVideos.contains(videoId)) {
      favoriteVideos.add(videoId);
      statusMessage.value = 'تم إضافة الفيديو للمفضلة';
    }
  }

  // إزالة فيديو من المفضلة
  void removeFromFavorites(String videoId) {
    if (favoriteVideos.remove(videoId)) {
      statusMessage.value = 'تم إزالة الفيديو من المفضلة';
    }
  }

  // التحقق من وجود فيديو في المفضلة
  bool isFavorite(String videoId) {
    return favoriteVideos.contains(videoId);
  }

  // مسح البحثات الأخيرة
  void clearRecentSearches() {
    recentSearches.clear();
    statusMessage.value = 'تم مسح البحثات الأخيرة';
  }

  // مسح نتائج البحث
  void clearSearchResults() {
    searchResults.clear();
    lastSearchQuery.value = '';
  }

  // تبديل حالة التشغيل
  void togglePlayPause() {
    if (isPlaying.value) {
      pauseVideo();
    } else {
      resumeVideo();
    }
  }
}
