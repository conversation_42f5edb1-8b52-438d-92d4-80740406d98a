class SettingModel {
  final int? id;
  final int userId;
  final String settingKey;
  final String settingValue;
  final String settingType;
  final DateTime createdAt;
  final DateTime updatedAt;

  SettingModel({
    this.id,
    required this.userId,
    required this.settingKey,
    required this.settingValue,
    required this.settingType,
    required this.createdAt,
    required this.updatedAt,
  });

  // تحويل من Map إلى SettingModel
  factory SettingModel.fromMap(Map<String, dynamic> map) {
    return SettingModel(
      id: map['id'],
      userId: map['user_id'],
      settingKey: map['setting_key'],
      settingValue: map['setting_value'],
      settingType: map['setting_type'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  // تحويل من SettingModel إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'setting_key': settingKey,
      'setting_value': settingValue,
      'setting_type': settingType,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // إنشاء نسخة محدثة من SettingModel
  SettingModel copyWith({
    int? id,
    int? userId,
    String? settingKey,
    String? settingValue,
    String? settingType,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SettingModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      settingKey: settingKey ?? this.settingKey,
      settingValue: settingValue ?? this.settingValue,
      settingType: settingType ?? this.settingType,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'SettingModel{id: $id, settingKey: $settingKey, settingValue: $settingValue}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SettingModel &&
        other.id == id &&
        other.userId == userId &&
        other.settingKey == settingKey &&
        other.settingValue == settingValue &&
        other.settingType == settingType;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        userId.hashCode ^
        settingKey.hashCode ^
        settingValue.hashCode ^
        settingType.hashCode;
  }
}

// أنواع الإعدادات
enum SettingType {
  string,
  boolean,
  integer,
  double_,
  json,
}

// مفاتيح الإعدادات المحددة مسبقاً
class SettingKeys {
  static const String language = 'language';
  static const String themeMode = 'theme_mode';
  static const String voiceConfidenceThreshold = 'voice_confidence_threshold';
  static const String autoExecuteCommands = 'auto_execute_commands';
  static const String enableVoiceAuthentication = 'enable_voice_authentication';
  static const String speechToTextLanguage = 'speech_to_text_language';
  static const String textToSpeechLanguage = 'text_to_speech_language';
  static const String recordingQuality = 'recording_quality';
  static const String maxRecordingDuration = 'max_recording_duration';
  static const String enableActivityLogging = 'enable_activity_logging';
  static const String enableNotifications = 'enable_notifications';
  static const String youtubeAutoPlay = 'youtube_auto_play';
  static const String contactsPermissionGranted = 'contacts_permission_granted';
  static const String microphonePermissionGranted = 'microphone_permission_granted';
  static const String phonePermissionGranted = 'phone_permission_granted';
}
