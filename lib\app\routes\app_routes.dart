abstract class Routes {
  Routes._();
  
  static const home = _Paths.home;
  static const contacts = _Paths.contacts;
  static const voiceSetup = _Paths.voiceSetup;
  static const settings = _Paths.settings;
  static const onboarding = _Paths.onboarding;
  static const userSetup = _Paths.userSetup;
}

abstract class _Paths {
  _Paths._();
  
  static const home = '/home';
  static const contacts = '/contacts';
  static const voiceSetup = '/voice-setup';
  static const settings = '/settings';
  static const onboarding = '/onboarding';
  static const userSetup = '/user-setup';
}
