import 'package:get/get.dart';
import 'package:contacts_service/contacts_service.dart';
import '../models/models.dart';
import '../services/services.dart';

class ContactsController extends GetxController {
  // الخدمات
  final PhoneService _phoneService = PhoneService();
  final DatabaseService _databaseService = DatabaseService();

  // المتغيرات التفاعلية
  final RxList<ContactModel> localContacts = <ContactModel>[].obs;
  final RxList<Contact> deviceContacts = <Contact>[].obs;
  final RxList<ContactModel> favoriteContacts = <ContactModel>[].obs;
  final RxList<ContactModel> searchResults = <ContactModel>[].obs;
  final RxBool isLoading = false.obs;
  final RxBool isSyncing = false.obs;
  final RxString searchQuery = ''.obs;
  final RxString statusMessage = ''.obs;
  final RxBool permissionsGranted = false.obs;

  // إحصائيات
  final RxInt totalContacts = 0.obs;
  final RxInt totalFavorites = 0.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeContacts();
  }

  // تهيئة جهات الاتصال
  Future<void> _initializeContacts() async {
    try {
      isLoading.value = true;
      statusMessage.value = 'جاري تحميل جهات الاتصال...';

      // التحقق من الصلاحيات
      await _checkPermissions();
      
      // تحميل جهات الاتصال المحلية
      await loadLocalContacts();
      
      // تحميل المفضلة
      await loadFavoriteContacts();

      statusMessage.value = 'تم تحميل جهات الاتصال بنجاح';
    } catch (e) {
      statusMessage.value = 'خطأ في تحميل جهات الاتصال: $e';
    } finally {
      isLoading.value = false;
    }
  }

  // التحقق من الصلاحيات
  Future<void> _checkPermissions() async {
    try {
      permissionsGranted.value = await _phoneService.checkPhonePermissions();
    } catch (e) {
      permissionsGranted.value = false;
    }
  }

  // طلب الصلاحيات
  Future<bool> requestPermissions() async {
    try {
      final granted = await _phoneService.requestPhonePermissions();
      permissionsGranted.value = granted;
      return granted;
    } catch (e) {
      permissionsGranted.value = false;
      return false;
    }
  }

  // تحميل جهات الاتصال المحلية
  Future<void> loadLocalContacts() async {
    try {
      final contacts = await _phoneService.getAllLocalContacts();
      localContacts.value = contacts;
      totalContacts.value = contacts.length;
    } catch (e) {
      statusMessage.value = 'خطأ في تحميل جهات الاتصال المحلية: $e';
    }
  }

  // تحميل جهات الاتصال من الجهاز
  Future<void> loadDeviceContacts() async {
    try {
      if (!permissionsGranted.value) {
        final granted = await requestPermissions();
        if (!granted) {
          statusMessage.value = 'يرجى منح صلاحية الوصول لجهات الاتصال';
          return;
        }
      }

      isLoading.value = true;
      statusMessage.value = 'جاري تحميل جهات الاتصال من الجهاز...';

      final contacts = await _phoneService.getDeviceContacts();
      deviceContacts.value = contacts;
      
      statusMessage.value = 'تم تحميل ${contacts.length} جهة اتصال من الجهاز';
    } catch (e) {
      statusMessage.value = 'خطأ في تحميل جهات الاتصال من الجهاز: $e';
    } finally {
      isLoading.value = false;
    }
  }

  // تحميل جهات الاتصال المفضلة
  Future<void> loadFavoriteContacts() async {
    try {
      final favorites = await _phoneService.getFavoriteContacts();
      favoriteContacts.value = favorites;
      totalFavorites.value = favorites.length;
    } catch (e) {
      statusMessage.value = 'خطأ في تحميل جهات الاتصال المفضلة: $e';
    }
  }

  // البحث في جهات الاتصال
  Future<void> searchContacts(String query) async {
    try {
      searchQuery.value = query;
      
      if (query.isEmpty) {
        searchResults.clear();
        return;
      }

      isLoading.value = true;
      final results = await _phoneService.searchLocalContacts(query);
      searchResults.value = results;
      
      statusMessage.value = 'تم العثور على ${results.length} نتيجة';
    } catch (e) {
      statusMessage.value = 'خطأ في البحث: $e';
    } finally {
      isLoading.value = false;
    }
  }

  // إضافة جهة اتصال جديدة
  Future<bool> addContact(String name, String phoneNumber, {String? email}) async {
    try {
      statusMessage.value = 'جاري إضافة جهة الاتصال...';

      // إضافة إلى قاعدة البيانات المحلية
      final success = await _phoneService.addContactToDatabase(name, phoneNumber, email: email);
      
      if (success) {
        // إعادة تحميل جهات الاتصال
        await loadLocalContacts();
        statusMessage.value = 'تم إضافة جهة الاتصال بنجاح';
        return true;
      } else {
        statusMessage.value = 'فشل في إضافة جهة الاتصال';
        return false;
      }
    } catch (e) {
      statusMessage.value = 'خطأ في إضافة جهة الاتصال: $e';
      return false;
    }
  }

  // إضافة جهة اتصال إلى الجهاز أيضاً
  Future<bool> addContactToDevice(String name, String phoneNumber, {String? email}) async {
    try {
      if (!permissionsGranted.value) {
        final granted = await requestPermissions();
        if (!granted) {
          statusMessage.value = 'يرجى منح صلاحية الوصول لجهات الاتصال';
          return false;
        }
      }

      statusMessage.value = 'جاري إضافة جهة الاتصال إلى الجهاز...';

      // إضافة إلى الجهاز
      final deviceSuccess = await _phoneService.addContactToDevice(name, phoneNumber, email: email);
      
      // إضافة إلى قاعدة البيانات المحلية
      final localSuccess = await _phoneService.addContactToDatabase(name, phoneNumber, email: email);
      
      if (deviceSuccess && localSuccess) {
        await loadLocalContacts();
        statusMessage.value = 'تم إضافة جهة الاتصال بنجاح';
        return true;
      } else {
        statusMessage.value = 'فشل في إضافة جهة الاتصال';
        return false;
      }
    } catch (e) {
      statusMessage.value = 'خطأ في إضافة جهة الاتصال: $e';
      return false;
    }
  }

  // تحديث جهة اتصال
  Future<bool> updateContact(ContactModel contact) async {
    try {
      statusMessage.value = 'جاري تحديث جهة الاتصال...';

      final success = await _phoneService.updateContact(contact);
      
      if (success) {
        await loadLocalContacts();
        await loadFavoriteContacts();
        statusMessage.value = 'تم تحديث جهة الاتصال بنجاح';
        return true;
      } else {
        statusMessage.value = 'فشل في تحديث جهة الاتصال';
        return false;
      }
    } catch (e) {
      statusMessage.value = 'خطأ في تحديث جهة الاتصال: $e';
      return false;
    }
  }

  // حذف جهة اتصال
  Future<bool> deleteContact(int contactId) async {
    try {
      statusMessage.value = 'جاري حذف جهة الاتصال...';

      final success = await _phoneService.deleteContact(contactId);
      
      if (success) {
        await loadLocalContacts();
        await loadFavoriteContacts();
        statusMessage.value = 'تم حذف جهة الاتصال بنجاح';
        return true;
      } else {
        statusMessage.value = 'فشل في حذف جهة الاتصال';
        return false;
      }
    } catch (e) {
      statusMessage.value = 'خطأ في حذف جهة الاتصال: $e';
      return false;
    }
  }

  // تبديل حالة المفضلة
  Future<bool> toggleFavorite(int contactId) async {
    try {
      final success = await _phoneService.toggleFavorite(contactId);
      
      if (success) {
        await loadLocalContacts();
        await loadFavoriteContacts();
        statusMessage.value = 'تم تحديث حالة المفضلة';
        return true;
      } else {
        statusMessage.value = 'فشل في تحديث حالة المفضلة';
        return false;
      }
    } catch (e) {
      statusMessage.value = 'خطأ في تحديث حالة المفضلة: $e';
      return false;
    }
  }

  // مزامنة جهات الاتصال من الجهاز
  Future<void> syncContactsFromDevice() async {
    try {
      if (!permissionsGranted.value) {
        final granted = await requestPermissions();
        if (!granted) {
          statusMessage.value = 'يرجى منح صلاحية الوصول لجهات الاتصال';
          return;
        }
      }

      isSyncing.value = true;
      statusMessage.value = 'جاري مزامنة جهات الاتصال...';

      final success = await _phoneService.syncContactsFromDevice();
      
      if (success) {
        await loadLocalContacts();
        statusMessage.value = 'تم مزامنة جهات الاتصال بنجاح';
      } else {
        statusMessage.value = 'فشل في مزامنة جهات الاتصال';
      }
    } catch (e) {
      statusMessage.value = 'خطأ في مزامنة جهات الاتصال: $e';
    } finally {
      isSyncing.value = false;
    }
  }

  // إجراء مكالمة
  Future<void> makeCall(String phoneNumber) async {
    try {
      statusMessage.value = 'جاري إجراء المكالمة...';
      
      final success = await _phoneService.makeCall(phoneNumber);
      
      if (success) {
        statusMessage.value = 'تم إجراء المكالمة';
      } else {
        statusMessage.value = 'فشل في إجراء المكالمة';
      }
    } catch (e) {
      statusMessage.value = 'خطأ في إجراء المكالمة: $e';
    }
  }

  // إرسال رسالة نصية
  Future<void> sendSMS(String phoneNumber, String message) async {
    try {
      statusMessage.value = 'جاري إرسال الرسالة...';
      
      final success = await _phoneService.sendSMS(phoneNumber, message);
      
      if (success) {
        statusMessage.value = 'تم إرسال الرسالة';
      } else {
        statusMessage.value = 'فشل في إرسال الرسالة';
      }
    } catch (e) {
      statusMessage.value = 'خطأ في إرسال الرسالة: $e';
    }
  }

  // إرسال رسالة واتساب
  Future<void> sendWhatsApp(String phoneNumber, String message) async {
    try {
      statusMessage.value = 'جاري إرسال رسالة واتساب...';
      
      final success = await _phoneService.sendWhatsApp(phoneNumber, message);
      
      if (success) {
        statusMessage.value = 'تم إرسال رسالة واتساب';
      } else {
        statusMessage.value = 'فشل في إرسال رسالة واتساب';
      }
    } catch (e) {
      statusMessage.value = 'خطأ في إرسال رسالة واتساب: $e';
    }
  }

  // البحث عن جهة اتصال بالاسم
  Future<ContactModel?> findContactByName(String name) async {
    try {
      return await _phoneService.findContactByName(name);
    } catch (e) {
      statusMessage.value = 'خطأ في البحث عن جهة الاتصال: $e';
      return null;
    }
  }

  // تنظيف نتائج البحث
  void clearSearchResults() {
    searchResults.clear();
    searchQuery.value = '';
  }

  // إعادة تحميل جميع البيانات
  Future<void> refreshData() async {
    await loadLocalContacts();
    await loadFavoriteContacts();
  }

  // الحصول على جهة اتصال بالمعرف
  ContactModel? getContactById(int id) {
    try {
      return localContacts.firstWhere((contact) => contact.id == id);
    } catch (e) {
      return null;
    }
  }

  // فلترة جهات الاتصال
  List<ContactModel> filterContacts(String filter) {
    if (filter.isEmpty) return localContacts;
    
    return localContacts.where((contact) =>
      contact.name.toLowerCase().contains(filter.toLowerCase()) ||
      contact.phoneNumber.contains(filter)
    ).toList();
  }
}
