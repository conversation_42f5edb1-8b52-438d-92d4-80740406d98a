import 'package:get/get.dart';

class AppTranslations extends Translations {
  @override
  Map<String, Map<String, String>> get keys => {
    'ar_SA': {
      // العامة
      'app_name': 'Smart Talk',
      'welcome': 'مرحباً',
      'loading': 'جاري التحميل...',
      'error': 'خطأ',
      'success': 'نجح',
      'cancel': 'إلغاء',
      'ok': 'موافق',
      'save': 'حفظ',
      'delete': 'حذف',
      'edit': 'تعديل',
      'add': 'إضافة',
      'search': 'بحث',
      'settings': 'الإعدادات',
      'back': 'رجوع',
      'next': 'التالي',
      'previous': 'السابق',
      'continue': 'متابعة',
      'skip': 'تخطي',
      'done': 'تم',
      'retry': 'إعادة المحاولة',
      
      // الشاشة الرئيسية
      'home': 'الرئيسية',
      'voice_commands': 'الأوامر الصوتية',
      'tap_mic_to_start': 'اضغط على الميكروفون لبدء الأوامر الصوتية',
      'listening': 'جاري الاستماع...',
      'processing': 'جاري المعالجة...',
      'command_executed': 'تم تنفيذ الأمر بنجاح',
      'command_failed': 'فشل في تنفيذ الأمر',
      'last_command': 'آخر أمر',
      'sound_level': 'مستوى الصوت',
      'quick_commands': 'أوامر سريعة',
      'statistics': 'الإحصائيات',
      
      // جهات الاتصال
      'contacts': 'جهات الاتصال',
      'add_contact': 'إضافة جهة اتصال',
      'edit_contact': 'تعديل جهة الاتصال',
      'delete_contact': 'حذف جهة الاتصال',
      'contact_name': 'اسم جهة الاتصال',
      'phone_number': 'رقم الهاتف',
      'email': 'البريد الإلكتروني',
      'favorites': 'المفضلة',
      'call': 'اتصال',
      'message': 'رسالة',
      'whatsapp': 'واتساب',
      'sync_contacts': 'مزامنة جهات الاتصال',
      'no_contacts_found': 'لا توجد جهات اتصال',
      'search_contacts': 'البحث في جهات الاتصال',
      
      // بصمة الصوت
      'voice_setup': 'إعداد بصمة الصوت',
      'voice_authentication': 'بصمة الصوت',
      'voice_print_security': 'بصمة الصوت للأمان',
      'voice_print_registered': 'بصمة الصوت مسجلة',
      'voice_print_not_registered': 'لم يتم تسجيل بصمة الصوت',
      'register_voice_print': 'تسجيل بصمة الصوت',
      'recording_voice_print': 'تسجيل بصمة الصوت',
      'recording_duration': 'مدة التسجيل',
      'start_recording': 'بدء التسجيل',
      'stop_recording': 'إيقاف التسجيل',
      'voice_threshold': 'عتبة الثقة',
      'test_voice_print': 'اختبار بصمة الصوت',
      'delete_voice_print': 'حذف بصمة الصوت',
      're_record_voice_print': 'إعادة تسجيل بصمة الصوت',
      
      // الإعدادات
      'general_settings': 'الإعدادات العامة',
      'voice_settings': 'إعدادات الصوت',
      'security_settings': 'إعدادات الأمان',
      'app_settings': 'إعدادات التطبيق',
      'language': 'اللغة',
      'theme': 'المظهر',
      'dark_mode': 'الوضع الليلي',
      'light_mode': 'الوضع النهاري',
      'notifications': 'الإشعارات',
      'activity_logging': 'تسجيل الأنشطة',
      'speech_language': 'لغة التعرف على الصوت',
      'tts_language': 'لغة التحدث',
      'recording_quality': 'جودة التسجيل',
      'max_recording_duration': 'مدة التسجيل القصوى',
      'confidence_threshold': 'عتبة الثقة',
      'reset_settings': 'إعادة تعيين الإعدادات',
      'clear_app_data': 'مسح بيانات التطبيق',
      'export_settings': 'تصدير الإعدادات',
      'import_settings': 'استيراد الإعدادات',
      'app_info': 'معلومات التطبيق',
      
      // الأوامر الصوتية
      'make_call': 'إجراء مكالمة',
      'send_sms': 'إرسال رسالة نصية',
      'send_whatsapp': 'إرسال واتساب',
      'open_youtube': 'فتح يوتيوب',
      'play_video': 'تشغيل فيديو',
      'pause_video': 'إيقاف مؤقت',
      'next_video': 'الفيديو التالي',
      'previous_video': 'الفيديو السابق',
      'search_contact': 'البحث عن جهة اتصال',
      
      // رسائل الخطأ
      'permission_denied': 'تم رفض الصلاحية',
      'microphone_permission_required': 'يرجى منح صلاحية الميكروفون',
      'contacts_permission_required': 'يرجى منح صلاحية جهات الاتصال',
      'phone_permission_required': 'يرجى منح صلاحية الهاتف',
      'voice_recognition_failed': 'فشل في التعرف على الصوت',
      'command_not_understood': 'لم يتم فهم الأمر',
      'no_internet_connection': 'لا يوجد اتصال بالإنترنت',
      
      // التطبيق الأولي
      'onboarding_title_1': 'مرحباً بك في Smart Talk',
      'onboarding_desc_1': 'تطبيق ذكي للتحكم في هاتفك باستخدام الأوامر الصوتية',
      'onboarding_title_2': 'أوامر صوتية ذكية',
      'onboarding_desc_2': 'اتصل، أرسل رسائل، وتحكم في YouTube بصوتك فقط',
      'onboarding_title_3': 'أمان بصمة الصوت',
      'onboarding_desc_3': 'حماية متقدمة تضمن تنفيذ الأوامر من صوتك فقط',
      'onboarding_title_4': 'دعم اللغتين',
      'onboarding_desc_4': 'يدعم التطبيق اللغتين العربية والإنجليزية بسلاسة',
      'get_started': 'ابدأ الآن',
      
      // إعداد المستخدم
      'user_setup': 'إعداد الملف الشخصي',
      'enter_your_info': 'أدخل بياناتك الأساسية لبدء استخدام التطبيق',
      'personal_info': 'المعلومات الشخصية',
      'name': 'الاسم',
      'enter_name': 'أدخل اسمك',
      'preferred_language': 'اللغة المفضلة',
      'app_theme': 'مظهر التطبيق',
      'auto_theme': 'تلقائي',
      'system_theme': 'حسب إعدادات النظام',
    },
    
    'en_US': {
      // General
      'app_name': 'Smart Talk',
      'welcome': 'Welcome',
      'loading': 'Loading...',
      'error': 'Error',
      'success': 'Success',
      'cancel': 'Cancel',
      'ok': 'OK',
      'save': 'Save',
      'delete': 'Delete',
      'edit': 'Edit',
      'add': 'Add',
      'search': 'Search',
      'settings': 'Settings',
      'back': 'Back',
      'next': 'Next',
      'previous': 'Previous',
      'continue': 'Continue',
      'skip': 'Skip',
      'done': 'Done',
      'retry': 'Retry',
      
      // Home Screen
      'home': 'Home',
      'voice_commands': 'Voice Commands',
      'tap_mic_to_start': 'Tap the microphone to start voice commands',
      'listening': 'Listening...',
      'processing': 'Processing...',
      'command_executed': 'Command executed successfully',
      'command_failed': 'Command execution failed',
      'last_command': 'Last Command',
      'sound_level': 'Sound Level',
      'quick_commands': 'Quick Commands',
      'statistics': 'Statistics',
      
      // Contacts
      'contacts': 'Contacts',
      'add_contact': 'Add Contact',
      'edit_contact': 'Edit Contact',
      'delete_contact': 'Delete Contact',
      'contact_name': 'Contact Name',
      'phone_number': 'Phone Number',
      'email': 'Email',
      'favorites': 'Favorites',
      'call': 'Call',
      'message': 'Message',
      'whatsapp': 'WhatsApp',
      'sync_contacts': 'Sync Contacts',
      'no_contacts_found': 'No contacts found',
      'search_contacts': 'Search Contacts',
      
      // Voice Setup
      'voice_setup': 'Voice Setup',
      'voice_authentication': 'Voice Authentication',
      'voice_print_security': 'Voice Print Security',
      'voice_print_registered': 'Voice print registered',
      'voice_print_not_registered': 'Voice print not registered',
      'register_voice_print': 'Register Voice Print',
      'recording_voice_print': 'Recording Voice Print',
      'recording_duration': 'Recording Duration',
      'start_recording': 'Start Recording',
      'stop_recording': 'Stop Recording',
      'voice_threshold': 'Confidence Threshold',
      'test_voice_print': 'Test Voice Print',
      'delete_voice_print': 'Delete Voice Print',
      're_record_voice_print': 'Re-record Voice Print',
      
      // Settings
      'general_settings': 'General Settings',
      'voice_settings': 'Voice Settings',
      'security_settings': 'Security Settings',
      'app_settings': 'App Settings',
      'language': 'Language',
      'theme': 'Theme',
      'dark_mode': 'Dark Mode',
      'light_mode': 'Light Mode',
      'notifications': 'Notifications',
      'activity_logging': 'Activity Logging',
      'speech_language': 'Speech Recognition Language',
      'tts_language': 'Text-to-Speech Language',
      'recording_quality': 'Recording Quality',
      'max_recording_duration': 'Max Recording Duration',
      'confidence_threshold': 'Confidence Threshold',
      'reset_settings': 'Reset Settings',
      'clear_app_data': 'Clear App Data',
      'export_settings': 'Export Settings',
      'import_settings': 'Import Settings',
      'app_info': 'App Information',
      
      // Voice Commands
      'make_call': 'Make Call',
      'send_sms': 'Send SMS',
      'send_whatsapp': 'Send WhatsApp',
      'open_youtube': 'Open YouTube',
      'play_video': 'Play Video',
      'pause_video': 'Pause Video',
      'next_video': 'Next Video',
      'previous_video': 'Previous Video',
      'search_contact': 'Search Contact',
      
      // Error Messages
      'permission_denied': 'Permission Denied',
      'microphone_permission_required': 'Microphone permission required',
      'contacts_permission_required': 'Contacts permission required',
      'phone_permission_required': 'Phone permission required',
      'voice_recognition_failed': 'Voice recognition failed',
      'command_not_understood': 'Command not understood',
      'no_internet_connection': 'No internet connection',
      
      // Onboarding
      'onboarding_title_1': 'Welcome to Smart Talk',
      'onboarding_desc_1': 'Smart app to control your phone using voice commands',
      'onboarding_title_2': 'Smart Voice Commands',
      'onboarding_desc_2': 'Call, send messages, and control YouTube with just your voice',
      'onboarding_title_3': 'Voice Print Security',
      'onboarding_desc_3': 'Advanced protection ensures commands are executed only by your voice',
      'onboarding_title_4': 'Bilingual Support',
      'onboarding_desc_4': 'The app supports both Arabic and English seamlessly',
      'get_started': 'Get Started',
      
      // User Setup
      'user_setup': 'User Setup',
      'enter_your_info': 'Enter your basic information to start using the app',
      'personal_info': 'Personal Information',
      'name': 'Name',
      'enter_name': 'Enter your name',
      'preferred_language': 'Preferred Language',
      'app_theme': 'App Theme',
      'auto_theme': 'Auto',
      'system_theme': 'Follow system settings',
    },
  };
}
