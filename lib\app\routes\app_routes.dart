abstract class Routes {
  Routes._();
  
  static const HOME = _Paths.HOME;
  static const CONTACTS = _Paths.CONTACTS;
  static const VOICE_SETUP = _Paths.VOICE_SETUP;
  static const SETTINGS = _Paths.SETTINGS;
  static const ONBOARDING = _Paths.ONBOARDING;
  static const USER_SETUP = _Paths.USER_SETUP;
}

abstract class _Paths {
  _Paths._();
  
  static const HOME = '/home';
  static const CONTACTS = '/contacts';
  static const VOICE_SETUP = '/voice-setup';
  static const SETTINGS = '/settings';
  static const ONBOARDING = '/onboarding';
  static const USER_SETUP = '/user-setup';
}
