import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'app/routes/app_pages.dart';
import 'app/themes/app_theme.dart';
import 'app/translations/app_translations.dart';
import 'app/services/database_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة قاعدة البيانات
  await _initializeDatabase();

  // تحديد الصفحة الأولى
  String initialRoute = await _determineInitialRoute();

  runApp(SmartTalkApp(initialRoute: initialRoute));
}

Future<void> _initializeDatabase() async {
  try {
    final databaseService = DatabaseService();
    // التأكد من إنشاء قاعدة البيانات
    await databaseService.getAllUsers();
  } catch (e) {
    print('Error initializing database: $e');
  }
}

Future<String> _determineInitialRoute() async {
  try {
    final databaseService = DatabaseService();
    final users = await databaseService.getAllUsers();

    // إذا كان هناك مستخدم، انتقل للصفحة الرئيسية
    if (users.isNotEmpty) {
      return '/home';
    }

    // إذا لم يكن هناك مستخدم، ابدأ بالـ onboarding
    return '/onboarding';
  } catch (e) {
    print('Error determining initial route: $e');
    return '/onboarding';
  }
}

class SmartTalkApp extends StatelessWidget {
  final String initialRoute;

  const SmartTalkApp({super.key, required this.initialRoute});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'Smart Talk',

      // الثيمات
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,

      // الترجمة
      translations: AppTranslations(),
      locale: const Locale('ar', 'SA'),
      fallbackLocale: const Locale('en', 'US'),

      // التوجيه
      initialRoute: initialRoute,
      getPages: AppPages.routes,

      // إعدادات إضافية
      debugShowCheckedModeBanner: false,
      defaultTransition: Transition.cupertino,
      transitionDuration: const Duration(milliseconds: 300),
    );
  }
}
