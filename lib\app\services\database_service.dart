import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';
import '../models/models.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // خدمات المستخدمين
  Future<int> insertUser(UserModel user) async {
    final db = await _databaseHelper.database;
    return await db.insert('users', user.toMap());
  }

  Future<UserModel?> getUserById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return UserModel.fromMap(maps.first);
    }
    return null;
  }

  Future<UserModel?> getFirstUser() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      limit: 1,
      orderBy: 'created_at ASC',
    );

    if (maps.isNotEmpty) {
      return UserModel.fromMap(maps.first);
    }
    return null;
  }

  Future<List<UserModel>> getAllUsers() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query('users');
    return List.generate(maps.length, (i) => UserModel.fromMap(maps[i]));
  }

  Future<int> updateUser(UserModel user) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'users',
      user.toMap(),
      where: 'id = ?',
      whereArgs: [user.id],
    );
  }

  Future<int> deleteUser(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'users',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // خدمات جهات الاتصال
  Future<int> insertContact(ContactModel contact) async {
    final db = await _databaseHelper.database;
    return await db.insert('contacts', contact.toMap());
  }

  Future<ContactModel?> getContactById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'contacts',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return ContactModel.fromMap(maps.first);
    }
    return null;
  }

  Future<List<ContactModel>> getAllContacts() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'contacts',
      orderBy: 'name ASC',
    );
    return List.generate(maps.length, (i) => ContactModel.fromMap(maps[i]));
  }

  Future<List<ContactModel>> searchContacts(String query) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'contacts',
      where: 'name LIKE ? OR phone_number LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
      orderBy: 'name ASC',
    );
    return List.generate(maps.length, (i) => ContactModel.fromMap(maps[i]));
  }

  Future<List<ContactModel>> getFavoriteContacts() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'contacts',
      where: 'is_favorite = ?',
      whereArgs: [1],
      orderBy: 'name ASC',
    );
    return List.generate(maps.length, (i) => ContactModel.fromMap(maps[i]));
  }

  Future<int> updateContact(ContactModel contact) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'contacts',
      contact.toMap(),
      where: 'id = ?',
      whereArgs: [contact.id],
    );
  }

  Future<int> deleteContact(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'contacts',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // خدمات بصمات الصوت
  Future<int> insertVoicePrint(VoicePrintModel voicePrint) async {
    final db = await _databaseHelper.database;
    return await db.insert('voice_prints', voicePrint.toMap());
  }

  Future<VoicePrintModel?> getVoicePrintByUserId(int userId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'voice_prints',
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'created_at DESC',
      limit: 1,
    );

    if (maps.isNotEmpty) {
      return VoicePrintModel.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateVoicePrint(VoicePrintModel voicePrint) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'voice_prints',
      voicePrint.toMap(),
      where: 'id = ?',
      whereArgs: [voicePrint.id],
    );
  }

  Future<int> deleteVoicePrint(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'voice_prints',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // خدمات الأوامر الصوتية
  Future<int> insertVoiceCommand(VoiceCommandModel command) async {
    final db = await _databaseHelper.database;
    return await db.insert('voice_commands', command.toMap());
  }

  Future<VoiceCommandModel?> getVoiceCommandById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'voice_commands',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return VoiceCommandModel.fromMap(maps.first);
    }
    return null;
  }

  Future<List<VoiceCommandModel>> getVoiceCommandsByUserId(int userId,
      {int? limit}) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'voice_commands',
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'created_at DESC',
      limit: limit,
    );
    return List.generate(
        maps.length, (i) => VoiceCommandModel.fromMap(maps[i]));
  }

  Future<List<VoiceCommandModel>> getVoiceCommandsByType(
      int userId, String commandType) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'voice_commands',
      where: 'user_id = ? AND command_type = ?',
      whereArgs: [userId, commandType],
      orderBy: 'created_at DESC',
    );
    return List.generate(
        maps.length, (i) => VoiceCommandModel.fromMap(maps[i]));
  }

  Future<int> updateVoiceCommand(VoiceCommandModel command) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'voice_commands',
      command.toMap(),
      where: 'id = ?',
      whereArgs: [command.id],
    );
  }

  Future<int> deleteVoiceCommand(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'voice_commands',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // خدمات الإعدادات
  Future<int> insertSetting(SettingModel setting) async {
    final db = await _databaseHelper.database;
    return await db.insert('settings', setting.toMap());
  }

  Future<SettingModel?> getSetting(int userId, String settingKey) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'settings',
      where: 'user_id = ? AND setting_key = ?',
      whereArgs: [userId, settingKey],
    );

    if (maps.isNotEmpty) {
      return SettingModel.fromMap(maps.first);
    }
    return null;
  }

  Future<List<SettingModel>> getAllSettings(int userId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'settings',
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'setting_key ASC',
    );
    return List.generate(maps.length, (i) => SettingModel.fromMap(maps[i]));
  }

  Future<int> updateSetting(SettingModel setting) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'settings',
      setting.toMap(),
      where: 'user_id = ? AND setting_key = ?',
      whereArgs: [setting.userId, setting.settingKey],
    );
  }

  Future<int> upsertSetting(SettingModel setting) async {
    final existing = await getSetting(setting.userId, setting.settingKey);
    if (existing != null) {
      return await updateSetting(setting.copyWith(id: existing.id));
    } else {
      return await insertSetting(setting);
    }
  }

  Future<int> deleteSetting(int userId, String settingKey) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'settings',
      where: 'user_id = ? AND setting_key = ?',
      whereArgs: [userId, settingKey],
    );
  }

  // خدمات سجل الأنشطة
  Future<int> insertActivityLog(ActivityLogModel activityLog) async {
    final db = await _databaseHelper.database;
    return await db.insert('activity_logs', activityLog.toMap());
  }

  Future<List<ActivityLogModel>> getActivityLogs(int userId,
      {int? limit}) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'activity_logs',
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'created_at DESC',
      limit: limit,
    );
    return List.generate(maps.length, (i) => ActivityLogModel.fromMap(maps[i]));
  }

  Future<List<ActivityLogModel>> getActivityLogsByType(
      int userId, String activityType) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'activity_logs',
      where: 'user_id = ? AND activity_type = ?',
      whereArgs: [userId, activityType],
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => ActivityLogModel.fromMap(maps[i]));
  }

  Future<int> deleteActivityLog(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'activity_logs',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<int> clearActivityLogs(int userId) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'activity_logs',
      where: 'user_id = ?',
      whereArgs: [userId],
    );
  }

  // إحصائيات
  Future<Map<String, int>> getStatistics(int userId) async {
    final db = await _databaseHelper.database;

    final contactsCount = Sqflite.firstIntValue(
            await db.rawQuery('SELECT COUNT(*) FROM contacts')) ??
        0;

    final commandsCount = Sqflite.firstIntValue(await db.rawQuery(
            'SELECT COUNT(*) FROM voice_commands WHERE user_id = ?',
            [userId])) ??
        0;

    final successfulCommandsCount = Sqflite.firstIntValue(await db.rawQuery(
            'SELECT COUNT(*) FROM voice_commands WHERE user_id = ? AND execution_status = ?',
            [userId, 'success'])) ??
        0;

    final activitiesCount = Sqflite.firstIntValue(await db.rawQuery(
            'SELECT COUNT(*) FROM activity_logs WHERE user_id = ?',
            [userId])) ??
        0;

    return {
      'contacts': contactsCount,
      'commands': commandsCount,
      'successful_commands': successfulCommandsCount,
      'activities': activitiesCount,
    };
  }

  // مسح جميع البيانات
  Future<void> clearAllData() async {
    final db = await _databaseHelper.database;
    await db.transaction((txn) async {
      await txn.delete('users');
      await txn.delete('contacts');
      await txn.delete('voice_commands');
      await txn.delete('voice_prints');
      await txn.delete('settings');
      await txn.delete('activity_logs');
    });
  }
}
