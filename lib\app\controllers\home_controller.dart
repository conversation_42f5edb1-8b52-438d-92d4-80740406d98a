import 'package:get/get.dart';
import '../models/models.dart';
import '../services/services.dart';
import '../utils/utils.dart';

class HomeController extends GetxController {
  // الخدمات
  final DatabaseService _databaseService = DatabaseService();
  final SpeechService _speechService = SpeechService();
  final VoiceAuthenticationService _voiceAuthService =
      VoiceAuthenticationService();
  final NLPService _nlpService = NLPService();
  final PhoneService _phoneService = PhoneService();
  final YouTubeService _youtubeService = YouTubeService();

  // Logger
  final Logger _logger = Logger();

  // المتغيرات التفاعلية
  final Rx<UserModel?> currentUser = Rx<UserModel?>(null);
  final RxBool isInitialized = false.obs;
  final RxBool isLoading = false.obs;
  final RxString statusMessage = ''.obs;
  final RxBool isListening = false.obs;
  final RxString lastCommand = ''.obs;
  final RxDouble soundLevel = 0.0.obs;
  final RxBool hasVoicePrint = false.obs;
  final RxBool permissionsGranted = false.obs;

  // إحصائيات
  final RxMap<String, int> statistics = <String, int>{}.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeApp();
  }

  @override
  void onReady() {
    super.onReady();
    _setupListeners();
  }

  // تهيئة التطبيق
  Future<void> _initializeApp() async {
    try {
      isLoading.value = true;
      statusMessage.value = 'جاري تهيئة التطبيق...';

      // تهيئة الخدمات
      await _speechService.initialize();

      // تحميل المستخدم الحالي
      await _loadCurrentUser();

      // التحقق من الصلاحيات
      await _checkPermissions();

      // تحميل الإحصائيات
      await _loadStatistics();

      isInitialized.value = true;
      statusMessage.value = 'مرحباً! اضغط على الميكروفون لبدء الأوامر الصوتية';
    } catch (e) {
      statusMessage.value = 'خطأ في تهيئة التطبيق: $e';
      _logger.e('Error initializing app: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // إعداد المستمعين
  void _setupListeners() {
    // الاستماع لحالة التسجيل
    _speechService.listeningStream.listen((listening) {
      isListening.value = listening;
    });

    // الاستماع لنتائج الكلام
    _speechService.speechResultStream.listen((result) {
      if (result.isNotEmpty) {
        lastCommand.value = result;
        _processVoiceCommand(result);
      }
    });

    // الاستماع لمستوى الصوت
    _speechService.soundLevelStream.listen((level) {
      soundLevel.value = level;
    });
  }

  // تحميل المستخدم الحالي
  Future<void> _loadCurrentUser() async {
    try {
      final user = await _databaseService.getFirstUser();
      if (user != null) {
        currentUser.value = user;
        hasVoicePrint.value = await _voiceAuthService.hasVoicePrint(user.id!);
      }
    } catch (e) {
      _logger.e('Error loading current user: $e');
    }
  }

  // التحقق من الصلاحيات
  Future<void> _checkPermissions() async {
    try {
      final phonePermissions = await _phoneService.checkPhonePermissions();
      permissionsGranted.value = phonePermissions;
    } catch (e) {
      _logger.e('Error checking permissions: $e');
    }
  }

  // تحميل الإحصائيات
  Future<void> _loadStatistics() async {
    try {
      if (currentUser.value != null) {
        final stats =
            await _databaseService.getStatistics(currentUser.value!.id!);
        statistics.value = stats;
      }
    } catch (e) {
      _logger.e('Error loading statistics: $e');
    }
  }

  // بدء الاستماع للأوامر الصوتية
  Future<void> startListening() async {
    try {
      if (!isInitialized.value) {
        statusMessage.value = 'يرجى انتظار تهيئة التطبيق';
        return;
      }

      if (!permissionsGranted.value) {
        final granted = await _phoneService.requestPhonePermissions();
        if (!granted) {
          statusMessage.value = 'يرجى منح الصلاحيات المطلوبة';
          return;
        }
        permissionsGranted.value = true;
      }

      statusMessage.value = 'استمع...';
      await _speechService.startListening();
    } catch (e) {
      statusMessage.value = 'خطأ في بدء الاستماع: $e';
      _logger.e('Error starting listening: $e');
    }
  }

  // إيقاف الاستماع
  Future<void> stopListening() async {
    try {
      await _speechService.stopListening();
      statusMessage.value = 'تم إيقاف الاستماع';
    } catch (e) {
      statusMessage.value = 'خطأ في إيقاف الاستماع: $e';
      _logger.e('Error stopping listening: $e');
    }
  }

  // معالجة الأمر الصوتي
  Future<void> _processVoiceCommand(String command) async {
    try {
      statusMessage.value = 'جاري معالجة الأمر...';

      // التحقق من بصمة الصوت إذا كانت مفعلة
      if (hasVoicePrint.value && currentUser.value != null) {
        // هنا يجب تسجيل الصوت والتحقق من البصمة
        // للتبسيط، سنتجاهل هذه الخطوة الآن
      }

      // تحليل الأمر باستخدام NLP
      final analysis = await _nlpService.analyzeCommand(command);

      // تنفيذ الأمر
      final success = await _executeCommand(analysis);

      // حفظ الأمر في قاعدة البيانات
      if (currentUser.value != null) {
        await _saveCommandToDatabase(analysis, success);
      }

      // تحديث الإحصائيات
      await _loadStatistics();

      if (success) {
        statusMessage.value = 'تم تنفيذ الأمر بنجاح';
      } else {
        statusMessage.value = 'فشل في تنفيذ الأمر';
      }
    } catch (e) {
      statusMessage.value = 'خطأ في معالجة الأمر: $e';
      _logger.e('Error processing voice command: $e');
    }
  }

  // تنفيذ الأمر
  Future<bool> _executeCommand(CommandAnalysis analysis) async {
    try {
      switch (analysis.intent) {
        case Intent.makeCall:
          return await _handleCallCommand(analysis);
        case Intent.sendSms:
          return await _handleSmsCommand(analysis);
        case Intent.sendWhatsapp:
          return await _handleWhatsappCommand(analysis);
        case Intent.openYoutube:
          return await _youtubeService.openYouTube();
        case Intent.playVideo:
          return await _handlePlayVideoCommand(analysis);
        case Intent.pauseVideo:
          return await _youtubeService.pauseVideo();
        case Intent.nextVideo:
          return await _youtubeService.nextVideo();
        case Intent.previousVideo:
          return await _youtubeService.previousVideo();
        case Intent.addContact:
          return await _handleAddContactCommand(analysis);
        case Intent.searchContact:
          return await _handleSearchContactCommand(analysis);
        default:
          return false;
      }
    } catch (e) {
      _logger.e('Error executing command: $e');
      return false;
    }
  }

  // معالجة أمر الاتصال
  Future<bool> _handleCallCommand(CommandAnalysis analysis) async {
    final contactName = analysis.entities['contact_name'];
    final phoneNumber = analysis.entities['phone_number'];

    if (phoneNumber != null) {
      return await _phoneService.makeCall(phoneNumber);
    } else if (contactName != null) {
      final contact = await _phoneService.findContactByName(contactName);
      if (contact != null) {
        return await _phoneService.makeCall(contact.phoneNumber);
      }
    }
    return false;
  }

  // معالجة أمر الرسالة النصية
  Future<bool> _handleSmsCommand(CommandAnalysis analysis) async {
    final contactName = analysis.entities['contact_name'];
    final phoneNumber = analysis.entities['phone_number'];
    final message = analysis.entities['message'] ?? 'مرحبا';

    if (phoneNumber != null) {
      return await _phoneService.sendSMS(phoneNumber, message);
    } else if (contactName != null) {
      final contact = await _phoneService.findContactByName(contactName);
      if (contact != null) {
        return await _phoneService.sendSMS(contact.phoneNumber, message);
      }
    }
    return false;
  }

  // معالجة أمر الواتساب
  Future<bool> _handleWhatsappCommand(CommandAnalysis analysis) async {
    final contactName = analysis.entities['contact_name'];
    final phoneNumber = analysis.entities['phone_number'];
    final message = analysis.entities['message'] ?? 'مرحبا';

    if (phoneNumber != null) {
      return await _phoneService.sendWhatsApp(phoneNumber, message);
    } else if (contactName != null) {
      final contact = await _phoneService.findContactByName(contactName);
      if (contact != null) {
        return await _phoneService.sendWhatsApp(contact.phoneNumber, message);
      }
    }
    return false;
  }

  // معالجة أمر تشغيل فيديو
  Future<bool> _handlePlayVideoCommand(CommandAnalysis analysis) async {
    final videoQuery = analysis.entities['video_query'];
    if (videoQuery != null) {
      return await _youtubeService.searchAndPlayVideo(videoQuery);
    }
    return false;
  }

  // معالجة أمر إضافة جهة اتصال
  Future<bool> _handleAddContactCommand(CommandAnalysis analysis) async {
    final contactName = analysis.entities['contact_name'];
    final phoneNumber = analysis.entities['phone_number'];
    final email = analysis.entities['email'];

    if (contactName != null && phoneNumber != null) {
      return await _phoneService.addContactToDatabase(contactName, phoneNumber,
          email: email);
    }
    return false;
  }

  // معالجة أمر البحث عن جهة اتصال
  Future<bool> _handleSearchContactCommand(CommandAnalysis analysis) async {
    final searchQuery = analysis.entities['search_query'];
    if (searchQuery != null) {
      final contacts = await _phoneService.searchLocalContacts(searchQuery);
      if (contacts.isNotEmpty) {
        statusMessage.value = 'تم العثور على ${contacts.length} جهة اتصال';
        return true;
      } else {
        statusMessage.value = 'لم يتم العثور على أي جهة اتصال';
        return false;
      }
    }
    return false;
  }

  // حفظ الأمر في قاعدة البيانات
  Future<void> _saveCommandToDatabase(
      CommandAnalysis analysis, bool success) async {
    try {
      if (currentUser.value != null) {
        final command = VoiceCommandModel(
          userId: currentUser.value!.id!,
          commandText: analysis.originalText,
          commandType: analysis.commandType.toString().split('.').last,
          intent: analysis.intent.toString().split('.').last,
          entities: analysis.entities,
          executionStatus: success ? 'success' : 'failed',
          confidenceScore: analysis.confidence,
          createdAt: DateTime.now(),
        );

        await _databaseService.insertVoiceCommand(command);
      }
    } catch (e) {
      _logger.e('Error saving command to database: $e');
    }
  }

  // تحديث رسالة الحالة
  void updateStatusMessage(String message) {
    statusMessage.value = message;
  }

  // إعادة تحميل البيانات
  Future<void> refreshData() async {
    await _loadCurrentUser();
    await _loadStatistics();
  }

  @override
  void onClose() {
    _speechService.dispose();
    super.onClose();
  }
}
