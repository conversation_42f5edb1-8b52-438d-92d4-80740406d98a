import 'dart:typed_data';

class VoicePrintModel {
  final int? id;
  final int userId;
  final Uint8List voiceData;
  final String voiceFeatures; // JSON string containing voice features
  final double confidenceThreshold;
  final DateTime createdAt;
  final DateTime updatedAt;

  VoicePrintModel({
    this.id,
    required this.userId,
    required this.voiceData,
    required this.voiceFeatures,
    this.confidenceThreshold = 0.8,
    required this.createdAt,
    required this.updatedAt,
  });

  // تحويل من Map إلى VoicePrintModel
  factory VoicePrintModel.fromMap(Map<String, dynamic> map) {
    return VoicePrintModel(
      id: map['id'],
      userId: map['user_id'],
      voiceData: map['voice_data'],
      voiceFeatures: map['voice_features'],
      confidenceThreshold: map['confidence_threshold']?.toDouble() ?? 0.8,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  // تحويل من VoicePrintModel إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'voice_data': voiceData,
      'voice_features': voiceFeatures,
      'confidence_threshold': confidenceThreshold,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // إنشاء نسخة محدثة من VoicePrintModel
  VoicePrintModel copyWith({
    int? id,
    int? userId,
    Uint8List? voiceData,
    String? voiceFeatures,
    double? confidenceThreshold,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return VoicePrintModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      voiceData: voiceData ?? this.voiceData,
      voiceFeatures: voiceFeatures ?? this.voiceFeatures,
      confidenceThreshold: confidenceThreshold ?? this.confidenceThreshold,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'VoicePrintModel{id: $id, userId: $userId, confidenceThreshold: $confidenceThreshold}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VoicePrintModel &&
        other.id == id &&
        other.userId == userId &&
        other.voiceFeatures == voiceFeatures &&
        other.confidenceThreshold == confidenceThreshold;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        userId.hashCode ^
        voiceFeatures.hashCode ^
        confidenceThreshold.hashCode;
  }
}

// نموذج لميزات الصوت المستخرجة
class VoiceFeatures {
  final List<double> mfcc; // Mel-frequency cepstral coefficients
  final double pitch;
  final double energy;
  final double spectralCentroid;
  final List<double> formants;
  final double duration;

  VoiceFeatures({
    required this.mfcc,
    required this.pitch,
    required this.energy,
    required this.spectralCentroid,
    required this.formants,
    required this.duration,
  });

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'mfcc': mfcc,
      'pitch': pitch,
      'energy': energy,
      'spectralCentroid': spectralCentroid,
      'formants': formants,
      'duration': duration,
    };
  }

  // تحويل من JSON
  factory VoiceFeatures.fromJson(Map<String, dynamic> json) {
    return VoiceFeatures(
      mfcc: List<double>.from(json['mfcc']),
      pitch: json['pitch'].toDouble(),
      energy: json['energy'].toDouble(),
      spectralCentroid: json['spectralCentroid'].toDouble(),
      formants: List<double>.from(json['formants']),
      duration: json['duration'].toDouble(),
    );
  }

  @override
  String toString() {
    return 'VoiceFeatures{pitch: $pitch, energy: $energy, duration: $duration}';
  }
}
