class VoiceCommandModel {
  final int? id;
  final int userId;
  final String commandText;
  final String commandType;
  final String intent;
  final Map<String, dynamic>? entities;
  final String executionStatus;
  final String? responseMessage;
  final String? audioFilePath;
  final double? confidenceScore;
  final int? executionTime; // بالميلي ثانية
  final DateTime createdAt;

  VoiceCommandModel({
    this.id,
    required this.userId,
    required this.commandText,
    required this.commandType,
    required this.intent,
    this.entities,
    required this.executionStatus,
    this.responseMessage,
    this.audioFilePath,
    this.confidenceScore,
    this.executionTime,
    required this.createdAt,
  });

  // تحويل من Map إلى VoiceCommandModel
  factory VoiceCommandModel.fromMap(Map<String, dynamic> map) {
    Map<String, dynamic>? entitiesMap;
    if (map['entities'] != null) {
      try {
        entitiesMap = Map<String, dynamic>.from(
          map['entities'] is String 
            ? {} // سيتم تحليل JSON هنا لاحقاً
            : map['entities']
        );
      } catch (e) {
        entitiesMap = null;
      }
    }

    return VoiceCommandModel(
      id: map['id'],
      userId: map['user_id'],
      commandText: map['command_text'],
      commandType: map['command_type'],
      intent: map['intent'],
      entities: entitiesMap,
      executionStatus: map['execution_status'],
      responseMessage: map['response_message'],
      audioFilePath: map['audio_file_path'],
      confidenceScore: map['confidence_score']?.toDouble(),
      executionTime: map['execution_time'],
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  // تحويل من VoiceCommandModel إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'command_text': commandText,
      'command_type': commandType,
      'intent': intent,
      'entities': entities?.toString(), // سيتم تحويل إلى JSON لاحقاً
      'execution_status': executionStatus,
      'response_message': responseMessage,
      'audio_file_path': audioFilePath,
      'confidence_score': confidenceScore,
      'execution_time': executionTime,
      'created_at': createdAt.toIso8601String(),
    };
  }

  // إنشاء نسخة محدثة من VoiceCommandModel
  VoiceCommandModel copyWith({
    int? id,
    int? userId,
    String? commandText,
    String? commandType,
    String? intent,
    Map<String, dynamic>? entities,
    String? executionStatus,
    String? responseMessage,
    String? audioFilePath,
    double? confidenceScore,
    int? executionTime,
    DateTime? createdAt,
  }) {
    return VoiceCommandModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      commandText: commandText ?? this.commandText,
      commandType: commandType ?? this.commandType,
      intent: intent ?? this.intent,
      entities: entities ?? this.entities,
      executionStatus: executionStatus ?? this.executionStatus,
      responseMessage: responseMessage ?? this.responseMessage,
      audioFilePath: audioFilePath ?? this.audioFilePath,
      confidenceScore: confidenceScore ?? this.confidenceScore,
      executionTime: executionTime ?? this.executionTime,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'VoiceCommandModel{id: $id, commandText: $commandText, intent: $intent, executionStatus: $executionStatus}';
  }
}

// أنواع الأوامر الصوتية
enum CommandType {
  call,           // اتصال
  sms,            // رسالة نصية
  whatsapp,       // واتساب
  youtube,        // يوتيوب
  contact,        // جهات الاتصال
  system,         // نظام
  unknown,        // غير معروف
}

// حالات تنفيذ الأوامر
enum ExecutionStatus {
  pending,        // في الانتظار
  processing,     // قيد المعالجة
  success,        // نجح
  failed,         // فشل
  cancelled,      // ملغي
}

// أنواع النوايا
enum Intent {
  makeCall,       // إجراء مكالمة
  sendSms,        // إرسال رسالة نصية
  sendWhatsapp,   // إرسال واتساب
  openYoutube,    // فتح يوتيوب
  playVideo,      // تشغيل فيديو
  pauseVideo,     // إيقاف مؤقت
  nextVideo,      // الفيديو التالي
  previousVideo,  // الفيديو السابق
  addContact,     // إضافة جهة اتصال
  searchContact,  // البحث عن جهة اتصال
  unknown,        // غير معروف
}
