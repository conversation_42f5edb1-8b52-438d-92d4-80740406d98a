import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../widgets/widgets.dart';

class OnboardingView extends StatefulWidget {
  const OnboardingView({super.key});

  @override
  State<OnboardingView> createState() => _OnboardingViewState();
}

class _OnboardingViewState extends State<OnboardingView> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<OnboardingPage> _pages = [
    OnboardingPage(
      title: 'مرحباً بك في Smart Talk',
      description: 'تطبيق ذكي للتحكم في هاتفك باستخدام الأوامر الصوتية',
      icon: Icons.waving_hand,
      color: Colors.blue,
    ),
    OnboardingPage(
      title: 'أوامر صوتية ذكية',
      description: 'اتصل، أرسل رسائل، وتحكم في YouTube بصوتك فقط',
      icon: Icons.mic,
      color: Colors.green,
    ),
    OnboardingPage(
      title: 'أمان بصمة الصوت',
      description: 'حماية متقدمة تضمن تنفيذ الأوامر من صوتك فقط',
      icon: Icons.security,
      color: Colors.orange,
    ),
    OnboardingPage(
      title: 'دعم اللغتين',
      description: 'يدعم التطبيق اللغتين العربية والإنجليزية بسلاسة',
      icon: Icons.language,
      color: Colors.purple,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // شريط التقدم
            _buildProgressBar(),

            // محتوى الصفحات
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemCount: _pages.length,
                itemBuilder: (context, index) {
                  return _buildPage(_pages[index]);
                },
              ),
            ),

            // أزرار التنقل
            _buildNavigationButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressBar() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: List.generate(_pages.length, (index) {
          return Expanded(
            child: Container(
              height: 4,
              margin: const EdgeInsets.symmetric(horizontal: 2),
              decoration: BoxDecoration(
                color: index <= _currentPage
                    ? Get.theme.colorScheme.primary
                    : Get.theme.colorScheme
                        .surfaceContainerHighest, // Cambiado de surfaceVariant a surfaceContainerHighest
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildPage(OnboardingPage page) {
    return Padding(
      padding: const EdgeInsets.all(32.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // الأيقونة
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: page.color.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              page.icon,
              size: 60,
              color: page.color,
            ),
          ),

          const SizedBox(height: 40),

          // العنوان
          Text(
            page.title,
            style: Get.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Get.theme.colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 20),

          // الوصف
          Text(
            page.description,
            style: Get.textTheme.bodyLarge?.copyWith(
              color: Get.theme.colorScheme.onSurface.withOpacity(0.7),
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // زر التخطي
          if (_currentPage < _pages.length - 1)
            TextButton(
              onPressed: () => _goToUserSetup(),
              child: Text(
                'تخطي',
                style: TextStyle(
                  color: Get.theme.colorScheme.onSurface.withOpacity(0.6),
                ),
              ),
            )
          else
            const SizedBox.shrink(),

          // مؤشر الصفحات
          Row(
            children: List.generate(_pages.length, (index) {
              return Container(
                width: 8,
                height: 8,
                margin: const EdgeInsets.symmetric(horizontal: 4),
                decoration: BoxDecoration(
                  color: index == _currentPage
                      ? Get.theme.colorScheme.primary
                      : Get.theme.colorScheme
                          .surfaceContainerHighest, // Cambiado de surfaceVariant a surfaceContainerHighest
                  shape: BoxShape.circle,
                ),
              );
            }),
          ),

          // زر التالي/البدء
          CustomButton(
            text: _currentPage == _pages.length - 1 ? 'ابدأ الآن' : 'التالي',
            onPressed: () {
              if (_currentPage == _pages.length - 1) {
                _goToUserSetup();
              } else {
                _pageController.nextPage(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                );
              }
            },
            icon: _currentPage == _pages.length - 1
                ? Icons.rocket_launch
                : Icons.arrow_forward,
          ),
        ],
      ),
    );
  }

  void _goToUserSetup() {
    Get.offNamed('/user-setup');
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }
}

class OnboardingPage {
  final String title;
  final String description;
  final IconData icon;
  final Color color;

  OnboardingPage({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });
}
