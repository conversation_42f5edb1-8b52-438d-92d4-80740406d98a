import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/models.dart';
import '../services/services.dart';

class UserSetupController extends GetxController {
  // الخدمات
  final DatabaseService _databaseService = DatabaseService();

  // Controllers للنماذج
  final TextEditingController nameController = TextEditingController();

  // المتغيرات التفاعلية
  final RxString selectedLanguage = 'ar'.obs;
  final RxString selectedTheme = 'light'.obs;
  final RxBool isLoading = false.obs;
  final RxString statusMessage = ''.obs;
  final RxBool canContinue = false.obs;

  @override
  void onInit() {
    super.onInit();
    _setupValidation();
  }

  // إعداد التحقق من صحة البيانات
  void _setupValidation() {
    nameController.addListener(() {
      _validateForm();
    });
  }

  // التحقق من صحة النموذج
  void _validateForm() {
    canContinue.value = nameController.text.trim().isNotEmpty;
  }

  // تحديث الاسم
  void updateName(String name) {
    _validateForm();
  }

  // تحديث اللغة
  void updateLanguage(String language) {
    selectedLanguage.value = language;
    
    // تطبيق اللغة فوراً
    final locale = language == 'ar' 
        ? const Locale('ar', 'SA') 
        : const Locale('en', 'US');
    Get.updateLocale(locale);
  }

  // تحديث المظهر
  void updateTheme(String theme) {
    selectedTheme.value = theme;
    
    // تطبيق المظهر فوراً
    ThemeMode themeMode;
    switch (theme) {
      case 'dark':
        themeMode = ThemeMode.dark;
        break;
      case 'light':
        themeMode = ThemeMode.light;
        break;
      default:
        themeMode = ThemeMode.system;
    }
    Get.changeThemeMode(themeMode);
  }

  // إنشاء المستخدم
  Future<void> createUser() async {
    if (!canContinue.value) {
      statusMessage.value = 'يرجى إدخال جميع البيانات المطلوبة';
      return;
    }

    try {
      isLoading.value = true;
      statusMessage.value = 'جاري إنشاء الملف الشخصي...';

      // إنشاء نموذج المستخدم
      final user = UserModel(
        name: nameController.text.trim(),
        language: selectedLanguage.value,
        themeMode: selectedTheme.value,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // حفظ المستخدم في قاعدة البيانات
      final userId = await _databaseService.insertUser(user);
      
      if (userId > 0) {
        statusMessage.value = 'تم إنشاء الملف الشخصي بنجاح';
        
        // إنشاء الإعدادات الافتراضية
        await _createDefaultSettings(userId);
        
        // الانتقال للشاشة الرئيسية
        await Future.delayed(const Duration(seconds: 1));
        Get.offAllNamed('/home');
      } else {
        statusMessage.value = 'خطأ في إنشاء الملف الشخصي';
      }
    } catch (e) {
      statusMessage.value = 'خطأ في إنشاء الملف الشخصي: $e';
      print('Error creating user: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // إنشاء الإعدادات الافتراضية
  Future<void> _createDefaultSettings(int userId) async {
    try {
      final defaultSettings = [
        SettingModel(
          userId: userId,
          settingKey: SettingKeys.language,
          settingValue: selectedLanguage.value,
          settingType: 'string',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        SettingModel(
          userId: userId,
          settingKey: SettingKeys.themeMode,
          settingValue: selectedTheme.value,
          settingType: 'string',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        SettingModel(
          userId: userId,
          settingKey: SettingKeys.enableVoiceAuthentication,
          settingValue: 'true',
          settingType: 'boolean',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        SettingModel(
          userId: userId,
          settingKey: SettingKeys.voiceConfidenceThreshold,
          settingValue: '0.8',
          settingType: 'double',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        SettingModel(
          userId: userId,
          settingKey: SettingKeys.enableNotifications,
          settingValue: 'true',
          settingType: 'boolean',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        SettingModel(
          userId: userId,
          settingKey: SettingKeys.enableActivityLogging,
          settingValue: 'true',
          settingType: 'boolean',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        SettingModel(
          userId: userId,
          settingKey: SettingKeys.speechToTextLanguage,
          settingValue: selectedLanguage.value == 'ar' ? 'ar-SA' : 'en-US',
          settingType: 'string',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        SettingModel(
          userId: userId,
          settingKey: SettingKeys.textToSpeechLanguage,
          settingValue: selectedLanguage.value == 'ar' ? 'ar-SA' : 'en-US',
          settingType: 'string',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        SettingModel(
          userId: userId,
          settingKey: SettingKeys.maxRecordingDuration,
          settingValue: '30',
          settingType: 'integer',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        SettingModel(
          userId: userId,
          settingKey: SettingKeys.recordingQuality,
          settingValue: 'high',
          settingType: 'string',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      for (final setting in defaultSettings) {
        await _databaseService.insertSetting(setting);
      }

      statusMessage.value = 'تم إعداد الإعدادات الافتراضية';
    } catch (e) {
      print('Error creating default settings: $e');
    }
  }

  // التحقق من وجود مستخدم مسبقاً
  Future<bool> checkExistingUser() async {
    try {
      final user = await _databaseService.getFirstUser();
      return user != null;
    } catch (e) {
      print('Error checking existing user: $e');
      return false;
    }
  }

  // تخطي الإعداد (للاختبار)
  void skipSetup() {
    Get.offAllNamed('/home');
  }

  @override
  void onClose() {
    nameController.dispose();
    super.onClose();
  }
}
