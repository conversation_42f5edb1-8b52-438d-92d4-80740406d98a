import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:smart_talk/main.dart';

void main() {
  group('Smart Talk App Tests', () {

    testWidgets('App should start correctly', (WidgetTester tester) async {
      // Build our app and trigger a frame
      await tester.pumpWidget(const SmartTalkApp(initialRoute: '/onboarding'));
      await tester.pumpAndSettle();

      // Verify that the app starts without errors
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('Home screen should show basic elements', (WidgetTester tester) async {
      // Build our app and trigger a frame
      await tester.pumpWidget(const SmartTalkApp(initialRoute: '/home'));
      await tester.pumpAndSettle();

      // Verify that basic elements are present
      expect(find.byType(Scaffold), findsOneWidget);
    });
  });

  group('Basic Tests', () {

    test('App should handle basic initialization', () {
      // Test basic app functionality
      expect(true, isTrue);
    });
  });
}