import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/settings_controller.dart';
import '../widgets/widgets.dart';

class SettingsView extends GetView<SettingsController> {
  const SettingsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: Obx(() => LoadingOverlay(
            isLoading: controller.isLoading.value,
            loadingMessage: 'جاري تحديث الإعدادات...',
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // معلومات المستخدم
                  _buildUserInfo(),

                  const SizedBox(height: 16),

                  // الإعدادات العامة
                  _buildGeneralSettings(),

                  const SizedBox(height: 16),

                  // إعدادات الصوت
                  _buildVoiceSettings(),

                  const SizedBox(height: 16),

                  // إعدادات الأمان
                  _buildSecuritySettings(),

                  const SizedBox(height: 16),

                  // إعدادات التطبيق
                  _buildAppSettings(),

                  const SizedBox(height: 16),

                  // معلومات التطبيق
                  _buildAppInfo(),
                ],
              ),
            ),
          )),
    );
  }

  Widget _buildUserInfo() {
    return Obx(() => Card(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Get.theme.colorScheme.primary,
                  child: Text(
                    controller.currentUser.value?.name.isNotEmpty == true
                        ? controller.currentUser.value!.name[0].toUpperCase()
                        : 'U',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        controller.currentUser.value?.name ?? 'مستخدم',
                        style: Get.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'اللغة: ${controller.currentLanguage.value == 'ar' ? 'العربية' : 'English'}',
                        style: Get.textTheme.bodyMedium?.copyWith(
                          color:
                              Get.theme.colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                      Text(
                        'المظهر: ${controller.isDarkMode.value ? 'داكن' : 'فاتح'}',
                        style: Get.textTheme.bodyMedium?.copyWith(
                          color:
                              Get.theme.colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ));
  }

  Widget _buildGeneralSettings() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الإعدادات العامة',
              style: Get.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // اختيار اللغة
            ListTile(
              leading: const Icon(Icons.language),
              title: const Text('اللغة'),
              subtitle: Obx(() => Text(controller.currentLanguage.value == 'ar'
                  ? 'العربية'
                  : 'English')),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showLanguageDialog(),
            ),

            const Divider(),

            // الوضع الليلي
            Obx(() => SwitchListTile(
                  secondary: const Icon(Icons.dark_mode),
                  title: const Text('الوضع الليلي'),
                  subtitle: const Text('تفعيل المظهر الداكن'),
                  value: controller.isDarkMode.value,
                  onChanged: (value) => controller.toggleDarkMode(),
                )),

            const Divider(),

            // الإشعارات
            Obx(() => SwitchListTile(
                  secondary: const Icon(Icons.notifications),
                  title: const Text('الإشعارات'),
                  subtitle: const Text('تفعيل إشعارات التطبيق'),
                  value: controller.enableNotifications.value,
                  onChanged: (value) => controller.updateGeneralSetting(
                      'enable_notifications', value),
                )),

            const Divider(),

            // تسجيل الأنشطة
            Obx(() => SwitchListTile(
                  secondary: const Icon(Icons.history),
                  title: const Text('تسجيل الأنشطة'),
                  subtitle: const Text('حفظ سجل الأوامر والأنشطة'),
                  value: controller.enableActivityLogging.value,
                  onChanged: (value) => controller.updateGeneralSetting(
                      'enable_activity_logging', value),
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildVoiceSettings() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات الصوت',
              style: Get.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // لغة التعرف على الصوت
            ListTile(
              leading: const Icon(Icons.mic),
              title: const Text('لغة التعرف على الصوت'),
              subtitle: Obx(() => Text(controller.speechLanguage.value)),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showSpeechLanguageDialog(),
            ),

            const Divider(),

            // لغة التحدث
            ListTile(
              leading: const Icon(Icons.record_voice_over),
              title: const Text('لغة التحدث'),
              subtitle: Obx(() => Text(controller.ttsLanguage.value)),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showTtsLanguageDialog(),
            ),

            const Divider(),

            // مدة التسجيل القصوى
            Obx(() => ListTile(
                  leading: const Icon(Icons.timer),
                  title: const Text('مدة التسجيل القصوى'),
                  subtitle:
                      Text('${controller.maxRecordingDuration.value} ثانية'),
                  trailing: SizedBox(
                    width: 100,
                    child: Slider(
                      value: controller.maxRecordingDuration.value.toDouble(),
                      min: 10,
                      max: 60,
                      divisions: 10,
                      onChanged: (value) => controller.updateVoiceSetting(
                          'max_recording_duration', value.toInt()),
                    ),
                  ),
                )),

            const Divider(),

            // جودة التسجيل
            ListTile(
              leading: const Icon(Icons.high_quality),
              title: const Text('جودة التسجيل'),
              subtitle: Obx(() => Text(
                  controller.recordingQuality.value == 'high'
                      ? 'عالية'
                      : 'متوسطة')),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showRecordingQualityDialog(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecuritySettings() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات الأمان',
              style: Get.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // تفعيل بصمة الصوت
            Obx(() => SwitchListTile(
                  secondary: const Icon(Icons.security),
                  title: const Text('بصمة الصوت'),
                  subtitle: const Text('التحقق من الهوية قبل تنفيذ الأوامر'),
                  value: controller.enableVoiceAuth.value,
                  onChanged: (value) => controller.updateVoiceSetting(
                      'enable_voice_authentication', value),
                )),

            const Divider(),

            // عتبة الثقة
            Obx(() => ListTile(
                  leading: const Icon(Icons.tune),
                  title: const Text('عتبة الثقة'),
                  subtitle: Text(
                      '${(controller.voiceConfidenceThreshold.value * 100).toInt()}%'),
                  trailing: SizedBox(
                    width: 100,
                    child: Slider(
                      value: controller.voiceConfidenceThreshold.value,
                      min: 0.5,
                      max: 1.0,
                      divisions: 10,
                      onChanged: (value) => controller.updateVoiceSetting(
                          'voice_confidence_threshold', value),
                    ),
                  ),
                )),

            const Divider(),

            // إعداد بصمة الصوت
            ListTile(
              leading: const Icon(Icons.fingerprint),
              title: const Text('إدارة بصمة الصوت'),
              subtitle: const Text('تسجيل أو تعديل بصمة الصوت'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => Get.toNamed('/voice-setup'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppSettings() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات التطبيق',
              style: Get.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // إعادة تعيين الإعدادات
            ListTile(
              leading: const Icon(Icons.restore, color: Colors.orange),
              title: const Text('إعادة تعيين الإعدادات'),
              subtitle: const Text('استعادة الإعدادات الافتراضية'),
              onTap: () => _showResetSettingsDialog(),
            ),

            const Divider(),

            // مسح بيانات التطبيق
            ListTile(
              leading: const Icon(Icons.delete_forever, color: Colors.red),
              title: const Text('مسح بيانات التطبيق'),
              subtitle: const Text('حذف جميع البيانات والإعدادات'),
              onTap: () => _showClearDataDialog(),
            ),

            const Divider(),

            // تصدير الإعدادات
            ListTile(
              leading: const Icon(Icons.file_upload),
              title: const Text('تصدير الإعدادات'),
              subtitle: const Text('حفظ الإعدادات في ملف'),
              onTap: () => _exportSettings(),
            ),

            const Divider(),

            // استيراد الإعدادات
            ListTile(
              leading: const Icon(Icons.file_download),
              title: const Text('استيراد الإعدادات'),
              subtitle: const Text('استعادة الإعدادات من ملف'),
              onTap: () => _importSettings(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppInfo() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات التطبيق',
              style: Get.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...controller.getAppInfo().entries.map((entry) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        entry.key,
                        style: Get.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        entry.value,
                        style: Get.textTheme.bodyMedium?.copyWith(
                          color:
                              Get.theme.colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }

  void _showLanguageDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('اختيار اللغة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('العربية'),
              value: 'ar',
              groupValue: controller.currentLanguage.value,
              onChanged: (value) {
                if (value != null) {
                  controller.changeLanguage(value);
                  Get.back();
                }
              },
            ),
            RadioListTile<String>(
              title: const Text('English'),
              value: 'en',
              groupValue: controller.currentLanguage.value,
              onChanged: (value) {
                if (value != null) {
                  controller.changeLanguage(value);
                  Get.back();
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showSpeechLanguageDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('لغة التعرف على الصوت'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('العربية السعودية'),
              value: 'ar-SA',
              groupValue: controller.speechLanguage.value,
              onChanged: (value) {
                if (value != null) {
                  controller.updateVoiceSetting(
                      'speech_to_text_language', value);
                  Get.back();
                }
              },
            ),
            RadioListTile<String>(
              title: const Text('English (US)'),
              value: 'en-US',
              groupValue: controller.speechLanguage.value,
              onChanged: (value) {
                if (value != null) {
                  controller.updateVoiceSetting(
                      'speech_to_text_language', value);
                  Get.back();
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showTtsLanguageDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('لغة التحدث'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('العربية السعودية'),
              value: 'ar-SA',
              groupValue: controller.ttsLanguage.value,
              onChanged: (value) {
                if (value != null) {
                  controller.updateVoiceSetting(
                      'text_to_speech_language', value);
                  Get.back();
                }
              },
            ),
            RadioListTile<String>(
              title: const Text('English (US)'),
              value: 'en-US',
              groupValue: controller.ttsLanguage.value,
              onChanged: (value) {
                if (value != null) {
                  controller.updateVoiceSetting(
                      'text_to_speech_language', value);
                  Get.back();
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showRecordingQualityDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('جودة التسجيل'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('عالية'),
              value: 'high',
              groupValue: controller.recordingQuality.value,
              onChanged: (value) {
                if (value != null) {
                  controller.updateVoiceSetting('recording_quality', value);
                  Get.back();
                }
              },
            ),
            RadioListTile<String>(
              title: const Text('متوسطة'),
              value: 'medium',
              groupValue: controller.recordingQuality.value,
              onChanged: (value) {
                if (value != null) {
                  controller.updateVoiceSetting('recording_quality', value);
                  Get.back();
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showResetSettingsDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('إعادة تعيين الإعدادات'),
        content: const Text(
            'هل أنت متأكد من رغبتك في إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              controller.resetSettings();
            },
            child: const Text('إعادة تعيين'),
          ),
        ],
      ),
    );
  }

  void _showClearDataDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('مسح بيانات التطبيق'),
        content: const Text(
            'تحذير: سيتم حذف جميع البيانات والإعدادات نهائياً. هذا الإجراء لا يمكن التراجع عنه.'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              controller.clearAppData();
            },
            child: const Text('مسح', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _exportSettings() async {
    final settings = await controller.exportSettings();
    if (settings.isNotEmpty) {
      Get.snackbar(
        'تصدير الإعدادات',
        'تم تصدير الإعدادات بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    }
  }

  void _importSettings() {
    Get.snackbar(
      'استيراد الإعدادات',
      'هذه الميزة قيد التطوير',
      backgroundColor: Colors.orange,
      colorText: Colors.white,
    );
  }
}
