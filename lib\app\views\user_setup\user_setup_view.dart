import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/user_setup_controller.dart';
import '../widgets/widgets.dart';

class UserSetupView extends GetView<UserSetupController> {
  const UserSetupView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Safe<PERSON><PERSON>(
        child: Obx(() => LoadingOverlay(
          isLoading: controller.isLoading.value,
          loadingMessage: 'جاري إعداد التطبيق...',
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 40),
                
                // العنوان الرئيسي
                _buildHeader(),
                
                const SizedBox(height: 40),
                
                // نموذج إدخال البيانات
                _buildUserForm(),
                
                const SizedBox(height: 30),
                
                // اختيار اللغة
                _buildLanguageSelection(),
                
                const SizedBox(height: 30),
                
                // اختيار المظهر
                _buildThemeSelection(),
                
                const SizedBox(height: 40),
                
                // زر المتابعة
                _buildContinueButton(),
                
                const SizedBox(height: 20),
                
                // رسالة الحالة
                _buildStatusMessage(),
              ],
            ),
          ),
        )),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          Icons.person_add,
          size: 48,
          color: Get.theme.colorScheme.primary,
        ),
        const SizedBox(height: 16),
        Text(
          'إعداد الملف الشخصي',
          style: Get.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'أدخل بياناتك الأساسية لبدء استخدام التطبيق',
          style: Get.textTheme.bodyLarge?.copyWith(
            color: Get.theme.colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildUserForm() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعلومات الشخصية',
              style: Get.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // حقل الاسم
            TextField(
              controller: controller.nameController,
              decoration: InputDecoration(
                labelText: 'الاسم *',
                hintText: 'أدخل اسمك',
                prefixIcon: const Icon(Icons.person),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                filled: true,
                fillColor: Get.theme.colorScheme.surface,
              ),
              textInputAction: TextInputAction.done,
              onChanged: (value) => controller.updateName(value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageSelection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'اللغة المفضلة',
              style: Get.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Obx(() => Column(
              children: [
                RadioListTile<String>(
                  title: const Text('العربية'),
                  subtitle: const Text('Arabic'),
                  value: 'ar',
                  groupValue: controller.selectedLanguage.value,
                  onChanged: (value) => controller.updateLanguage(value!),
                  secondary: const Icon(Icons.language),
                ),
                RadioListTile<String>(
                  title: const Text('English'),
                  subtitle: const Text('الإنجليزية'),
                  value: 'en',
                  groupValue: controller.selectedLanguage.value,
                  onChanged: (value) => controller.updateLanguage(value!),
                  secondary: const Icon(Icons.language),
                ),
              ],
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildThemeSelection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'مظهر التطبيق',
              style: Get.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Obx(() => Column(
              children: [
                RadioListTile<String>(
                  title: const Text('فاتح'),
                  subtitle: const Text('المظهر النهاري'),
                  value: 'light',
                  groupValue: controller.selectedTheme.value,
                  onChanged: (value) => controller.updateTheme(value!),
                  secondary: const Icon(Icons.light_mode),
                ),
                RadioListTile<String>(
                  title: const Text('داكن'),
                  subtitle: const Text('المظهر الليلي'),
                  value: 'dark',
                  groupValue: controller.selectedTheme.value,
                  onChanged: (value) => controller.updateTheme(value!),
                  secondary: const Icon(Icons.dark_mode),
                ),
                RadioListTile<String>(
                  title: const Text('تلقائي'),
                  subtitle: const Text('حسب إعدادات النظام'),
                  value: 'system',
                  groupValue: controller.selectedTheme.value,
                  onChanged: (value) => controller.updateTheme(value!),
                  secondary: const Icon(Icons.auto_mode),
                ),
              ],
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildContinueButton() {
    return Obx(() => CustomButton(
      text: 'متابعة',
      icon: Icons.arrow_forward,
      onPressed: controller.canContinue.value 
          ? controller.createUser 
          : null,
      width: double.infinity,
      height: 56,
      isLoading: controller.isLoading.value,
    ));
  }

  Widget _buildStatusMessage() {
    return Obx(() => controller.statusMessage.value.isNotEmpty
        ? Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: controller.statusMessage.value.contains('خطأ')
                  ? Colors.red.withOpacity(0.1)
                  : Get.theme.colorScheme.primaryContainer.withOpacity(0.3),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: controller.statusMessage.value.contains('خطأ')
                    ? Colors.red.withOpacity(0.3)
                    : Get.theme.colorScheme.primary.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  controller.statusMessage.value.contains('خطأ')
                      ? Icons.error_outline
                      : Icons.info_outline,
                  color: controller.statusMessage.value.contains('خطأ')
                      ? Colors.red
                      : Get.theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    controller.statusMessage.value,
                    style: Get.textTheme.bodyMedium?.copyWith(
                      color: controller.statusMessage.value.contains('خطأ')
                          ? Colors.red
                          : Get.theme.colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
          )
        : const SizedBox.shrink());
  }
}
