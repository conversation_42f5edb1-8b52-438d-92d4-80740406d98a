# Smart Talk 🎤

تطبيق ذكي للتحكم في الهاتف باستخدام الأوامر الصوتية مع دعم بصمة الصوت للأمان.

## 🌟 الميزات الرئيسية

### 🎯 أوامر صوتية ذكية
- **المكالمات**: "اتصل بأحمد" أو "Call Ahmed"
- **الرسائل النصية**: "أرسل رسالة إلى سارة قل لها مرحبا"
- **واتساب**: "أرسل واتساب إلى محمد"
- **يوتيوب**: "افتح يوتيوب" أو "شغل فيديو عن الطبخ"
- **جهات الاتصال**: "ابحث عن جهة اتصال أحمد"

### 🔒 أمان بصمة الصوت
- تسجيل بصمة صوتية فريدة لكل مستخدم
- التحقق من الهوية قبل تنفيذ الأوامر الحساسة
- عتبة ثقة قابلة للتخصيص (50%-100%)
- حماية من الأوامر غير المصرح بها

### 🌍 دعم متعدد اللغات
- **العربية**: دعم كامل للأوامر الصوتية باللغة العربية
- **الإنجليزية**: دعم كامل للأوامر الصوتية باللغة الإنجليزية
- واجهة مترجمة بالكامل
- تبديل سهل بين اللغات

### 🎨 تصميم عصري
- Material Design 3
- الوضع الليلي والنهاري
- أنيميشن تفاعلي للصوت
- واجهة سهلة الاستخدام

## 🏗️ البنية التقنية

### التقنيات المستخدمة
- **Flutter**: إطار العمل الأساسي
- **GetX**: إدارة الحالة والتوجيه
- **SQLite**: قاعدة البيانات المحلية
- **Speech-to-Text**: تحويل الكلام إلى نص
- **Text-to-Speech**: تحويل النص إلى كلام
- **NLP**: معالجة اللغة الطبيعية

### بنية المشروع
```
lib/
├── app/
│   ├── bindings/          # ربط Controllers
│   ├── controllers/       # منطق التطبيق
│   ├── database/          # قاعدة البيانات
│   ├── models/           # نماذج البيانات
│   ├── routes/           # التوجيه
│   ├── services/         # الخدمات
│   ├── themes/           # الثيمات
│   ├── translations/     # الترجمات
│   └── views/            # واجهات المستخدم
└── main.dart
```

## 📱 لقطات الشاشة

### الشاشة الرئيسية
- زر الميكروفون التفاعلي
- عرض حالة الاستماع
- مستوى الصوت المباشر
- الأوامر السريعة
- الإحصائيات

### إعداد بصمة الصوت
- تسجيل بصمة صوتية
- اختبار البصمة
- تعديل عتبة الثقة
- إعادة التسجيل

### إدارة جهات الاتصال
- عرض جميع جهات الاتصال
- البحث والفلترة
- إضافة وتعديل جهات الاتصال
- المفضلة
- مزامنة مع جهات اتصال الهاتف

## 🚀 التثبيت والتشغيل

### المتطلبات
- Flutter SDK (3.0.0 أو أحدث)
- Dart SDK (2.17.0 أو أحدث)
- Android Studio أو VS Code
- جهاز Android (API 21+) أو iOS (12.0+)

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/smart_talk.git
cd smart_talk
```

2. **تثبيت المكتبات**
```bash
flutter pub get
```

3. **تشغيل التطبيق**
```bash
flutter run
```

### إعداد إضافي

#### Android
- تأكد من تفعيل صلاحيات الميكروفون وجهات الاتصال
- للتحكم في YouTube، قد تحتاج لتثبيت تطبيق YouTube

#### iOS
- تأكد من إضافة مفاتيح الصلاحيات في Info.plist
- اختبر على جهاز حقيقي للحصول على أفضل أداء للصوت

## 🎮 كيفية الاستخدام

### الإعداد الأولي
1. افتح التطبيق لأول مرة
2. اتبع خطوات الـ Onboarding
3. أدخل اسمك واختر اللغة والثيم
4. اذهب لإعداد بصمة الصوت (اختياري)

### استخدام الأوامر الصوتية
1. اضغط على زر الميكروفون في الشاشة الرئيسية
2. انتظر حتى يظهر "جاري الاستماع..."
3. قل أمرك بوضوح
4. انتظر تنفيذ الأمر

### أمثلة على الأوامر

#### العربية
- "اتصل بأحمد"
- "أرسل رسالة إلى سارة قل لها كيف حالك"
- "أرسل واتساب إلى محمد"
- "افتح يوتيوب"
- "شغل فيديو عن الطبخ"
- "ابحث عن جهة اتصال علي"

#### الإنجليزية
- "Call Ahmed"
- "Send message to Sara say hello"
- "Send WhatsApp to Mohammed"
- "Open YouTube"
- "Play video about cooking"
- "Search contact Ali"

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
# اختبارات الوحدة
flutter test

# اختبارات التكامل
flutter test integration_test/
```

### أنواع الاختبارات
- **اختبارات النماذج**: التحقق من صحة تحويل البيانات
- **اختبارات الخدمات**: اختبار منطق الأعمال
- **اختبارات Controllers**: اختبار إدارة الحالة
- **اختبارات الواجهة**: اختبار تفاعل المستخدم

## 🔧 التخصيص والتطوير

### إضافة أوامر جديدة
1. أضف النية الجديدة في `voice_command_model.dart`
2. حدث `nlp_service.dart` لتحليل الأمر
3. أضف منطق التنفيذ في `home_controller.dart`

### إضافة لغة جديدة
1. أضف الترجمات في `app_translations.dart`
2. حدث إعدادات اللغة في `settings_controller.dart`
3. اختبر الأوامر الصوتية باللغة الجديدة

### تخصيص الثيم
1. عدل الألوان في `app_theme.dart`
2. أضف ثيمات جديدة حسب الحاجة
3. حدث منطق تبديل الثيم

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. أنشئ فرع للميزة الجديدة (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. افتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- **المطور**: فريق Smart Talk
- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: https://smarttalk.app

## 🙏 شكر وتقدير

- فريق Flutter لإطار العمل الرائع
- مجتمع GetX للمكتبة المفيدة
- جميع المساهمين في المكتبات مفتوحة المصدر المستخدمة

---

**Smart Talk** - تحكم في هاتفك بصوتك! 🎤✨
